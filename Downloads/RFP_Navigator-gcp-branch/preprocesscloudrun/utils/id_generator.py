"""
Utility module for generating unique identifiers.

This module provides functions for creating consistent unique identifiers
from input strings, which can be used across different services.
"""

import hashlib

def generate_unique_id(input_string):
    """
    Generate a unique identifier from an input string using SHA-256 hashing.
    
    Args:
        input_string (str): The input string to hash.
        
    Returns:
        str: A hexadecimal string representation of the SHA-256 hash.
    
    Example:
        >>> generate_unique_id("example_rfp")
        '4f9154a0086f5c10d92a464c3a077f5ca371d8f2b848520958b5159edf624f89'
    """
    # Normalize the input string by trimming whitespace and converting to lowercase
    input_string = input_string.strip().lower()
    
    # Generate SHA-256 hash from the input string
    hash_object = hashlib.sha256(input_string.encode())
    
    # Return the hexadecimal representation of the hash
    return hash_object.hexdigest()
