"""
Utility module for Google Cloud Storage operations.

This module provides common functions for interacting with Google Cloud Storage,
including uploading, downloading, and generating signed URLs for files.
"""

import os
import datetime
from google.cloud import storage
from google.oauth2 import service_account

def get_storage_client(credentials_env_var="GOOGLE_APPLICATION_CREDENTIALS"):
    """
    Get a Google Cloud Storage client using credentials from environment variables.
    
    Args:
        credentials_env_var (str): The name of the environment variable containing
                                  the path to the service account credentials file.
                                  Defaults to "GOOGLE_APPLICATION_CREDENTIALS".
    
    Returns:
        storage.Client: A Google Cloud Storage client.
    
    Raises:
        ValueError: If the specified environment variable is not set.
    """
    credentials_path = os.getenv(credentials_env_var)
    
    if not credentials_path:
        # If the environment variable isn't set, try using default authentication
        return storage.Client()
    
    # Create credentials from the service account file
    credentials = service_account.Credentials.from_service_account_file(credentials_path)
    
    # Return a storage client with the credentials
    return storage.Client(credentials=credentials)

def upload_blob(bucket_name, source_file_name, destination_blob_name, credentials_env_var="GOOGLE_APPLICATION_CREDENTIALS"):
    """
    Upload a file to Google Cloud Storage.
    
    Args:
        bucket_name (str): The name of the GCS bucket.
        source_file_name (str): The path to the local file to upload.
        destination_blob_name (str): The path in GCS where the file should be stored.
        credentials_env_var (str): The name of the environment variable containing
                                  the path to the service account credentials file.
    
    Returns:
        str: The public URL of the uploaded file.
    
    Example:
        >>> upload_blob("my-bucket", "local_file.txt", "folder/file.txt")
        'gs://my-bucket/folder/file.txt'
    """
    # Get the storage client
    storage_client = get_storage_client(credentials_env_var)
    
    # Get the bucket
    bucket = storage_client.bucket(bucket_name)
    
    # Create a blob object
    blob = bucket.blob(destination_blob_name)
    
    # Upload the file
    blob.upload_from_filename(source_file_name)
    
    print(f"File {source_file_name} uploaded to {destination_blob_name}.")
    
    # Return the public URL
    return f"gs://{bucket_name}/{destination_blob_name}"

def download_blob(bucket_name, source_blob_name, destination_file_name, credentials_env_var="GOOGLE_APPLICATION_CREDENTIALS"):
    """
    Download a file from Google Cloud Storage.
    
    Args:
        bucket_name (str): The name of the GCS bucket.
        source_blob_name (str): The path in GCS to the file to download.
        destination_file_name (str): The local path where the file should be saved.
        credentials_env_var (str): The name of the environment variable containing
                                  the path to the service account credentials file.
    
    Example:
        >>> download_blob("my-bucket", "folder/file.txt", "local_file.txt")
    """
    # Get the storage client
    storage_client = get_storage_client(credentials_env_var)
    
    # Get the bucket
    bucket = storage_client.bucket(bucket_name)
    
    # Create a blob object
    blob = bucket.blob(source_blob_name)
    
    # Download the file
    blob.download_to_filename(destination_file_name)
    
    print(f"File {source_blob_name} downloaded to {destination_file_name}.")

def generate_signed_url(bucket_name, blob_name, expiration=3600, credentials_env_var="GOOGLE_APPLICATION_CREDENTIALS"):
    """
    Generate a signed URL for accessing a file in Google Cloud Storage.
    
    Args:
        bucket_name (str): The name of the GCS bucket.
        blob_name (str): The path in GCS to the file.
        expiration (int): The number of seconds the signed URL should be valid for.
                         Defaults to 3600 (1 hour).
        credentials_env_var (str): The name of the environment variable containing
                                  the path to the service account credentials file.
    
    Returns:
        str: A signed URL that can be used to access the file.
    
    Example:
        >>> generate_signed_url("my-bucket", "folder/file.txt", 7200)
        'https://storage.googleapis.com/my-bucket/folder/file.txt?X-Goog-Algorithm=...'
    """
    # Get the storage client
    storage_client = get_storage_client(credentials_env_var)
    
    # Get the bucket
    bucket = storage_client.bucket(bucket_name)
    
    # Create a blob object
    blob = bucket.blob(blob_name)
    
    # Generate a signed URL for the blob
    url = blob.generate_signed_url(
        expiration=datetime.timedelta(seconds=expiration),
        method="GET"  # Allow 'GET' method (read access)
    )
    
    return url
