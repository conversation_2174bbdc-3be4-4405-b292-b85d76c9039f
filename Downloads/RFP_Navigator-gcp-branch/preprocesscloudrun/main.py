from google.cloud import storage
import io
import pandas as pd
import requests
import weaviate
import os
import time
from unstructured.partition.xlsx import partition_xlsx
import pandas as pd
import numpy as np
import json
import threading
from concurrent.futures import ThreadPoolExecutor
from llama_index.core.bridge.pydantic import BaseModel, Field
from llama_index.core.program import LLMTextCompletionProgram
from bs4 import BeautifulSoup
from google.cloud import storage
import openai
from langchain.retrievers.weaviate_hybrid_search import WeaviateHybridSearchRetriever
from langchain_openai import OpenAI
from langchain_core.documents import Document
from llama_index.core.output_parsers import PydanticOutputParser
from llama_index.llms.openai import OpenAI
import ast
import nltk
import hashlib
from flask_cors import CORS
from flask import Flask, request , jsonify

nltk.download('punkt_tab')
nltk.download('stopwords')
nltk.download('wordnet')
nltk.download('averaged_perceptron_tagger')
nltk.download('averaged_perceptron_tagger_eng')

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

def generate_unique_id(input_string):
    # Ensure the input is a single word (you could handle multi-word inputs too if needed)
    input_string = input_string.strip().lower()  # Normalize by trimming and converting to lowercase
    # Generate SHA256 hash from the input string
    hash_object = hashlib.sha256(input_string.encode())
    return hash_object.hexdigest()  # Return the hexadecimal representation of the hash

def process_file_from_gcs(bucket_name, file_name):
    """Download the file from GCS and process it."""
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(file_name)

    file_stream = io.BytesIO()
    blob.download_to_file(file_stream)   # Download file content into an in-memory file
    file_stream.seek(0)                  # Move to the beginning of the file

    # Load the Excel file using Pandas
    excel_file = pd.ExcelFile(file_stream, engine='openpyxl')

    # Print the DataFrame or perform your processing logic
    return excel_file

idx = 0
# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Get API keys from environment variables
openai_api_key = os.getenv("OPENAI_API_KEY")
weaviate_api_key = os.getenv("WEAVIATE_API_KEY")

# Validate API keys
if not openai_api_key:
    raise ValueError("OPENAI_API_KEY environment variable not set or empty")
if not weaviate_api_key:
    raise ValueError("WEAVIATE_API_KEY environment variable not set or empty")

os.environ['OPENAI_API_KEY'] = openai_api_key
auth_config = weaviate.AuthApiKey(api_key=weaviate_api_key)# Get Weaviate URL from environment variables
weaviate_url = os.getenv("WEAVIATE_URL")
if not weaviate_url:
    raise ValueError("WEAVIATE_URL environment variable not set or empty")

client = weaviate.Client(
            url=weaviate_url,
            auth_client_secret=auth_config,
            additional_headers={
                    "X-Openai-Api-Key": openai_api_key,
                }
            )

retriever = WeaviateHybridSearchRetriever(
        alpha = 0.65,               # defaults to 0.5, which is equal weighting between keyword and semantic search
        client = client,           # keyword arguments to pass to the Weaviate client
        index_name = "RFP_RAG",    # The name of the index to use
        text_key = "page_content",
        k = 2,
        attributes = ["tags", "text_as_html", "filename", "excel","excel_link"]
    )



#Add empty row between Text
def insert_empty_rows(df):
    new_rows = []
    df.replace(r'^\s*$', np.nan, regex=True, inplace=True)
    i = 0
    while i < len(df) - 1:
        current_row = df.iloc[i]
        next_row = df.iloc[i + 1]

        if(current_row.count() == 1 and next_row.count() == 1):

            # Append an empty row
            new_rows.append(pd.Series([np.nan] * df.shape[1], index=df.columns))
        new_rows.append(current_row)
        i += 1

    # Check if the last row was added
    if i == len(df) - 1:
        new_rows.append(df.iloc[i])

    # Create a new DataFrame from the new rows
    new_df = pd.DataFrame(new_rows).reset_index(drop=True)
    return new_df

@app.route('/preprocess', methods=['POST'])
def preprocess():
    start_time = time.time()
    data = request.json
    excel_file_name = data.get('excel_file_name')  # Get the RFP name
    rfp_bucket_name = data.get('rfp_bucket_name')  # Get the RFP bucket
    excel_file = process_file_from_gcs(rfp_bucket_name, excel_file_name)
    sheet_names = excel_file.sheet_names
    original_filename = os.path.splitext(os.path.basename(excel_file_name))[0]
    unique_id = generate_unique_id(original_filename)


    # Get Firestore API URL from environment variables
    firestore_API_URL = os.getenv("FIRESTORE_API_URL")
    if not firestore_API_URL:
        raise ValueError("FIRESTORE_API_URL environment variable not set or empty")
    firestore_API_URL = firestore_API_URL + "/" + original_filename
    firestore_payload = {
    "key": "excel",
    "value": unique_id
    }
    try:
        response = requests.post(firestore_API_URL, json=firestore_payload)
        if response.status_code == 201:
            print("name added successfully.")
    except requests.exceptions.RequestException as e:
            print(f"An error occurred: {str(e)}", 500)

    print("Sheet names:", sheet_names)
    output_dir = 'Worksheets'
    os.makedirs(output_dir, exist_ok=True)
    llm = OpenAI()

    class TableInfo(BaseModel):
        """Information regarding a structured table."""

        table_name: str = Field(
            ..., description="table name (must be underscores and NO spaces)"
        )
        table_summary: str = Field(
            ..., description="detailed summary of the table"
        )
        table_tags: list = Field(
            [], description="list of tags (strings) that describe the table"
        )

    class TableInfoQuestions(BaseModel):
        questions: list = Field([], description="Get the list of questions")


    # Define the prompt for name and summary of the table
    prompt_str = """\
        Content:
            {table_str}

        In case there is no content related to table or revelant data, return null.
        Just directly return empty string or null datatype no need of explanation of your findings.

        Using the above table data, suggest a name for the table that does not match any of the following names: {exclude_table_name_list}

        - Avoid using generic table names such as 'table' or 'my_table'.

        Generate a comprehensive and detailed summary of the content and integrate information about the table's columns in a manner that facilitates understanding and interpretation of any queries related to the table.

        In case the content resembles a table with rows and columns, then catch the columns to create appropriate tags for those table's columns to enhance its searchability and usefulness when answering questions using hybrid search.

        """

    prompt_str_Questions =""""\
        Content:
            {table_str}
        Role:
            Your role now is to generate questions for the provided worksheet data that will be used or suggested to the clients for querying later.
        Instruction:
            Based on the content or data provided, generate a list of 2 potential questions that would help user know more about the worksheet.
            Try to use only the information provided, not prior knowledge. Try to understand what the content is appropriately about to avoid unrelevant questiosn.
            Cross check whether the questions are apt for the content.
        Requirements:
            Do not provide answers to the questions; only list the questions themselves.
        The questions should be:
            Relevant and directly related to the content or data. Insightful, helping to uncover data for fast grasping
            Aimed at effectively exploring the data analytical approach. It could question which could be answered using sql queries too.
        Formatting Guidelines:
            Ensure each question is clear, concise, and free of ambiguity.

    """

    #Define the format of the output.
    Table_Program = LLMTextCompletionProgram.from_defaults(
        output_parser=PydanticOutputParser(output_cls=TableInfo),
        llm=llm,
        prompt_template_str=prompt_str,
    )

    Table_Program_Questions = LLMTextCompletionProgram.from_defaults(
        output_parser=PydanticOutputParser(output_cls=TableInfoQuestions),
        llm=llm,
        prompt_template_str=prompt_str_Questions,
    )


    def Create_Table_Summary(table, table_names, sheet_name):
        """
        Create a summary of the table.

        Args:
        - table: The input table object or data.
        - table_names: Set containing names of already processed tables.

        Returns:
        - table_info: Information about the processed table.
        """
        try:

            programs = [
                (Table_Program,{"table_str":str(table),"exclude_table_name_list":str(list(table_names)) }),
            ]

            def execute_program(program,*args,**kwargs):
                return program(*args,**kwargs)


            with ThreadPoolExecutor() as executor:
                futures = [
                    executor.submit(execute_program,program,**kwargs)
                    for program, kwargs in programs
                ]
                result = [future.result() for future in futures]

            table_info = result[0]


        except Exception as e:
            print(f"Error encountered during table processing: {e}")

        # Ensure output directory exists
        output_dir = 'Table_JSON1'
        os.makedirs(output_dir, exist_ok=True)

        # Extract and print processed table name
        table_name = table_info.table_name
        print(f"Processed table: {table_name}")


        return table_info


    # Creating Worsheet Summary
    class SheetInfo(BaseModel):
        """Information regarding a unstructured worksheet."""

        sheet_summary: str = Field(
            ..., description="detailed summary of the worksheet"
        )
        sheet_tags: list = Field(
            [], description="list of tags (strings) that describe the worksheet"
        )

    # Define the prompt for naming and summarizing the worksheet
    prompt_str_sheet = """
    Content:
            {table_str}
    Provide a comprehensive and detailed summary of the worksheet in JSON format to facilitate identification of questions related to its content.

    Craft a comprehensive summary of the worksheet's content, which includes a mix of tables and text.

    Generate relevant tags for the tables to enhance searchability and aid in answering questions using hybrid search.

    Summary: """


    Sheet_program = LLMTextCompletionProgram.from_defaults(
        output_parser=PydanticOutputParser(output_cls=SheetInfo),
        llm=llm,
        prompt_template_str=prompt_str_sheet,
    )

    def Create_Worksheet_Summary(table):
        """
        Create a summary of the table.

        Args:
        - table: Input table to summarize.

        Returns:
        - table_info: Information extracted from the table.
        """
        try:
            if len(str(table)) > 30000:
                print("Input table exceeds 30,000 characters. Consider truncating for optimal processing.")

            print("db")
            print(table)
            # Process up to 30,000 characters of the table
            programs = [
                (Sheet_program,{"table_str":str(table)}),
                (Table_Program_Questions,{"table_str":str(table)})
            ]

            def execute_program(program,*args,**kwargs):
                return program(*args,**kwargs)


            with ThreadPoolExecutor() as executor:
                futures = [
                    executor.submit(execute_program,program,**kwargs)
                    for program, kwargs in programs
                ]
                result = [future.result() for future in futures]

            table_info, questions = result
            output_dir = 'Summary_JSON'
            os.makedirs(output_dir, exist_ok=True)
            print(table_info)
            return table_info, questions

        except Exception as e:
            print(f"Error occurred: {e}")


    # Upload Table to Cloud Storage
    bucket_name = os.getenv("RFP_TABLES_BUCKET")
    if not bucket_name:
        raise ValueError("RFP_TABLES_BUCKET environment variable not set or empty")
    def upload_blob(bucket_name, source_file_name, destination_blob_name):
        """Uploads a file to the bucket."""
        # The ID of your GCS bucket

        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        print(destination_blob_name)
        blob = bucket.blob(destination_blob_name)
        print(bucket_name)
        blob.upload_from_filename(source_file_name)
        print(
            f"File {source_file_name} uploaded to {destination_blob_name}."
        )

    Tables = []
    Texts = []
    Documents = []
    os.makedirs(original_filename, exist_ok=True)
    lock= threading.Lock()
    print(f"Execution time before preprocess:  {(time.time()-start_time):.5f} seconds.........\n")
    start_time = time.time()
    Questions = []
    table_dict = {}
    table_header_dict = {}

    def process_sheet(count, sheet_name):
        global idx
        table_names = set()
        df = pd.read_excel(excel_file, sheet_name=sheet_name)

        # Insert empty rows between specified rows
        new_df = insert_empty_rows(df)
        # new_df = df

        # Save the file as an Excel file
        output_file_path = os.path.join(output_dir, f'{sheet_name}.xlsx')
        new_df.to_excel(output_file_path, index=False)
        print(f"Sheet '{sheet_name}' has been saved as '{output_file_path}'")

        # Now partition the file
        with lock:
            timed= time.time()
            elements = partition_xlsx(file = output_file_path)
            print(f"partition_xlsx:  {(time.time()-timed):.5f} seconds.........\n")
        if(len(elements)==0):
            return
        metadata = elements[0].metadata.to_dict()
        Table_Text = [' ']
        table_path = [' ']
        table_tags = [[]]
        text_as_html = ''
        prev = ''
        unique = 0
        worksheet_text = ''

        for element in elements:
          if(element.metadata.text_as_html == None):
                worksheet_text += element.text + ' '
          else:
                txt = element.metadata.text_as_html
                soup = BeautifulSoup(txt, 'html.parser')
                table = soup.find('table')
                df = pd.read_html(str(table))[0]
                question = f"You are an expert in table detection. I will provide you with a dataframe, and you need to determine whether it is correctly classified as a table or not: {df}? Please respond with only 'yes' or 'no'."
                response = llm.complete(prompt=question)
                print(response)
                if str(response).strip().lower() == 'no':
                    worksheet_text += element.text + ' '
                else:
                    if txt != prev:
                        prev = txt
                        with lock:
                            timed = time.time()
                            table_info = Create_Table_Summary("" + str(txt), table_names, sheet_name)
                            print(f"Create_Table_Summary:  {(time.time()-timed):.5f} seconds.........\n")
                        elements[0].metadata.page_name = table_info.table_name
                        elements[0].metadata.page_number = count
                        elements[0].metadata.tags = table_info.table_tags
                        metadata = elements[0].metadata.to_dict()
                        metadata['category'] = 'Table'
                        with lock:
                            timed = time.time()
                            Tables.append(Document(page_content=str(table_info.table_summary) , metadata=metadata))
                            print(f"Tables:  {(time.time()-timed):.5f} seconds.........\n")
                        path = os.path.join(original_filename, sheet_name, f"""{table_info.table_name + '_' + str(unique)}.csv""")
                        directory = os.path.dirname(path)
                        os.makedirs(directory, exist_ok=True)
                        df.to_csv(path)
                        with lock:
                            table_dict[table_info.table_name + '_' + str(unique)] = df
                            table_header_dict[table_info.table_name + '_' + str(unique)] = df.head(3)
                        print(path)
                        # The path to your file to upload
                        source_file_name = path
                        unique += 1
                        destination_blob_name = path
                        upload_blob(bucket_name, source_file_name, destination_blob_name)
                        text_as_html += table_info.table_summary + ' '
                        # text_as_html += ','.join(table_info.table_tags)
                        # text_as_html += "\\n"
                        elements[0].metadata.text_as_html = path
                        Table_Text.append(table_info.table_name + ' '+ table_info.table_summary + element.text)
                        table_path.append(path)
                        table_tags.append(table_info.table_tags)


        # Get the text information
        text_info = [element.text for element in elements]
        bigger_text = ''
        for text in text_info:
                bigger_text += ' ' + text
        with lock :
            timed = time.time()
            summary_info, table_questions = Create_Worksheet_Summary(bigger_text + text_as_html)
            print(table_questions)
            Questions.append(table_questions)
            print(f"Create_worksheet_Summary:  {(time.time()-timed):.5f} seconds.........\n")
        # elements[0].metadata.tags = summary_info.sheet_tags
        metadata = elements[0].metadata.to_dict()

        # Save the information
        if bigger_text != '':
          elements[0].metadata.text_as_html = None
          metadata = elements[0].metadata.to_dict()
          with lock:
                Texts.append(Document(page_content=str(bigger_text) , metadata=metadata))

        destination_blob_name = os.path.join(original_filename, output_file_path)
        source_file_name = output_file_path
        # Get worksheet bucket name from environment variables
        bucket = os.getenv("RFP_WORKSHEET_BUCKET")
        if not bucket:
            raise ValueError("RFP_WORKSHEET_BUCKET environment variable not set or empty")
        upload_blob(bucket, source_file_name, destination_blob_name)

        for index, table in enumerate(Table_Text):
            idx += 1
            metadata['category'] = 'Table'
            string = str(summary_info.sheet_summary) + worksheet_text + table
            metadata['text_as_html'] = table_path[index]
            metadata['tags'] = summary_info.sheet_tags + table_tags[index]
            metadata['excel'] = unique_id
            metadata['excel_link'] = destination_blob_name
            metadata['worksheet'] = os.path.basename(output_file_path)
            with lock:
                Documents.append(Document(page_content = str(count) + " " + string, metadata=metadata.copy(), ids = idx))


    with ThreadPoolExecutor() as executor:
        list(executor.map(lambda x: process_sheet(*x), enumerate(sheet_names)))
    for doc in Documents:
        for md in doc.metadata:
            doc.metadata[md] = str(doc.metadata[md])

    retriever.add_documents(Documents)
    analyze = {}

    analyze['n_worksheets'] = len(sheet_names)
    print(table_header_dict)
    print(f"Execution time for the LLM calls and preprocess:  {(time.time()-start_time):.5f} seconds.........\n")
    print(Questions)
    prompt_question  = f"return a python list of the best 5 questions out of this based on usefulness {Questions}"
    question_response = llm.complete(prompt=prompt_question)
    print(str(question_response))

    analyze["questions"] = ast.literal_eval(str(question_response))
    # analyze["questions"] = json.loads(str(question_response))


    #@title Hybrid Search Retriever
    # Set OpenAI API key from environment variable
    openai.api_key = openai_api_key

    identify_product_query = f"You are a product table detector. Provided with a dictionary {table_header_dict} where each key is the name of a DataFrame, and each value is the first three rows of that DataFrame. Your task is to determine which DataFrame most likely contains product information. Most important only return just the name of the DataFrame that is most relevant to a product table, based on headers or data that suggest product-related content or None if you cannot find any in the list.just return the dictionary key or none "
    identify_product_query = f"""
Here's an improved version of your prompt with clearer instructions to ensure high standards for detecting product tables:

You are a product table detector.
Given a dictionary {table_header_dict} where each key represents the name of a DataFrame and each value contains the first three rows of that DataFrame, your task is to identify which DataFrame most likely contains product-related information.

Your decision should be based on the following criteria:

Relevant column headers or data that indicate the table is related to products (e.g., product names, product IDs, prices, categories, etc.).
If none of the tables show strong indicators of being a product table, return None.
Your response should only include the name of the DataFrame that is most relevant to a product table based on the headers or data. If no table meets the criteria, return None.

Important:

Do not return any DataFrame unless it strongly suggests product-related content.
Do not make assumptions or accept vague or irrelevant tables as product-related.
Ensure the table you choose clearly represents product-related information.
just return one word answer"""
    identify_legal_query = f"""You are a legal table detector. Provided with a dictionary {table_header_dict} where each key is the name of a DataFrame, and each value is the first three rows of that DataFrame. Your task is to determine which DataFrame most likely contains legal information. Most important only return just the name of the DataFrame that is most relevant to a legal table, based on headers or data that suggest legal-related content or None if you cannot find any in the list. If no table meets the criteria, return None.

Important:

Do not return any DataFrame unless it strongly suggests legal-related content.
Do not make assumptions or accept vague or irrelevant tables as legal-related.
Ensure the table you choose clearly represents legal-related information.
just return one word answer"""
    identify_location_query = f"""You are a location table detector. Provided with a dictionary {table_header_dict} where each key is the name of a DataFrame, and each value is the first three rows of that DataFrame. Your task is to determine which DataFrame most likely contains location information. Most important only return just the name of the DataFrame that is most relevant to a location table, based on headers or data that suggest location-related content or None if you cannot find any in the list. If no table meets the criteria, return None.

Important:

Do not return any DataFrame unless it strongly suggests location-related content.
Do not make assumptions or accept vague or irrelevant tables as location-related.
Ensure the table you choose clearly represents location-related information.
just return one word answer"""
    response = llm.complete(prompt=identify_product_query)
    print(response)
    answer = []
    if str(response).strip().lower() != 'none':
        prompt = f"""
        Given a DataFrame {table_dict[str(response)]}

        Generate a JSON structure with:
        1. A "data" key containing the rows as a list of dictionaries.
        2. A "columns" key where each item is a dictionary with "field" as the column name, and "headerName" as the formatted column name (capitalize each word).
        """
        answer = llm.complete(prompt=prompt)
        analyze["product"] = json.loads(str(answer))
        analyze["n_products"] = len(json.loads(answer.text)["data"])
    else:
        analyze["product"] = None
        analyze["n_products"] = 0

    response = llm.complete(prompt=identify_legal_query)
    print(response)

    if str(response).strip().lower() != 'none':
        prompt = f"""
        Given a DataFrame {table_dict[str(response)]}

        Generate a JSON structure with:
        1. A "data" key containing the rows as a list of dictionaries.
        2. A "columns" key where each item is a dictionary with "field" as the column name, and "headerName" as the formatted column name (capitalize each word).
        """
        answer = llm.complete(prompt=prompt)
        # print(answer)
        analyze["legal"] = json.loads(str(answer))
        analyze["n_legal"] = len(json.loads(answer.text)["data"])
    else:
        analyze["legal"] = None
        analyze["n_legal"] = 0

    print(response)
    response = llm.complete(prompt=identify_location_query)
    print(response)
    if str(response).strip().lower() != 'none':
        prompt = f"""
        Given a DataFrame {table_dict[str(response)]}

        Generate a JSON structure with:
        1. A "data" key containing the rows as a list of dictionaries.
        2. A "columns" key where each item is a dictionary with "field" as the column name, and "headerName" as the formatted column name (capitalize each word).
        """
        answer = llm.complete(prompt=prompt)
        print(answer)
        analyze["location"] = json.loads(str(answer))
        analyze["n_location"] = len(json.loads(answer.text)["data"])
    else:
        analyze["location"] = None
        analyze["n_location"] = 0

    print(response)
    print("BEFORE DUE DATE")
    '''
    QUERY_API_URL = "https://query-1046256773081.us-central1.run.app/query"
    query = {
    "query": "Provide the RFP due date in date format (maximum 3 words) or else return Not Available if you cannot find one",
    "rfp_name": original_filename
    }
    try:
        response = requests.post(QUERY_API_URL, json=query)
        print(response)
        analyze["due_date"] = json.loads(response.text)["data"]
        # print(list(json(response.text).response[0]))
        if response.status_code == 201:
            print("Analyze added successfully.")
    except requests.exceptions.RequestException as e:
            print(f"An error occurred: {str(e)}", 500)
    '''
    analyze["due_date"] = None
    common_rfp_questions = [
    "What is the scope of work for this project?",
    "What is the timeline or deadline for the project completion?",
    "What are the evaluation criteria for selecting a vendor?",
    "Are there any specific requirements for the technology stack or tools?",
    "What is the budget or price range for the project?"
    ]
    analyze["common_question"] = common_rfp_questions
    analyze["rfp_name"] = original_filename
    # timed = time.time()

    firestore_payload = {
    "key": "analyze",
    "value": analyze
    }
    try:
        response = requests.post(firestore_API_URL, json=firestore_payload)
        if response.status_code == 201:
            print("Analyze added successfully.")
    except requests.exceptions.RequestException as e:
            print(f"An error occurred: {str(e)}", 500)


    firestore_payload = {
    "key": "status",
    "value": 1
    }
    try:
        response = requests.post(firestore_API_URL, json=firestore_payload)
        if response.status_code == 201:
            print("Status added successfully.")
    except requests.exceptions.RequestException as e:
            print(f"An error occurred: {str(e)}", 500)

    print("finish")
    return jsonify("finish")



if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
