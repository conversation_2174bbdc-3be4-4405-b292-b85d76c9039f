# Environment Variables Setup

This document explains how to set up environment variables for the RFP Navigator application.

## Overview

The RFP Navigator is composed of several microservices, each requiring its own set of environment variables. Each service has its own `.env` file that contains the environment variables specific to that service.

## Environment Files

Each microservice has its own `.env` file:

- `query/.env` - Environment variables for the Query service
- `delete/.env` - Environment variables for the Delete service
- `firestore-api/.env` - Environment variables for the Firestore API service
- `reanalyze/.env` - Environment variables for the Reanalyze service
- `preprocesscloudrun/.env` - Environment variables for the Preprocess service
- `rfp-navigator-ui/.env` - Environment variables for the UI

## Setting Up Environment Variables

1. Copy the `.env.example` file to create a new `.env` file for each service:

```bash
cp query/.env.example query/.env
cp delete/.env.example delete/.env
cp firestore-api/.env.example firestore-api/.env
cp reanalyze/.env.example reanalyze/.env
cp preprocesscloudrun/.env.example preprocesscloudrun/.env
cp rfp-navigator-ui/.env.example rfp-navigator-ui/.env
```

2. Edit each `.env` file to set the appropriate values for your environment.

## Required Environment Variables

### Common Variables

These variables are required by most services:

- `PROJECT_ID` - Your Google Cloud Project ID
- `REGION` - The Google Cloud region (e.g., us-central1)
- `GOOGLE_APPLICATION_CREDENTIALS` - Path to your Google Cloud service account key file

### API Keys

- `OPENAI_API_KEY` - Your OpenAI API key
- `GOOGLE_API_KEY` - Your Google API key
- `LANGCHAIN_API_KEY` - Your LangChain API key (if using LangSmith)

### Weaviate Settings

- `WEAVIATE_URL` - Your Weaviate instance URL
- `WEAVIATE_API_KEY` - Your Weaviate API key

### Storage Buckets

- `STAGING_BUCKET` - The GCS bucket for staging files
- `RFP_TABLES_BUCKET` - The GCS bucket for RFP tables
- `RFP_WORKSHEET_BUCKET` - The GCS bucket for RFP worksheets

### Service URLs

- `QUERY_SERVICE_URL` - URL for the Query service
- `FIRESTORE_API_URL` - URL for the Firestore API service
- `DELETE_SERVICE_URL` - URL for the Delete service
- `PREPROCESS_SERVICE_URL` - URL for the Preprocess service

### UI-Specific Variables

- `REACT_APP_API_BASE_URL` - Base URL for the API
- `REACT_APP_QUERY_API` - URL for the Query API
- `REACT_APP_SUGGESTIONS_API` - URL for the Suggestions API
- `REACT_APP_GET_DATA` - URL for the Get Data API
- `REACT_APP_REANALYZE` - URL for the Reanalyze API

### Firebase Configuration (UI only)

- `REACT_APP_FIREBASE_API_KEY` - Firebase API key
- `REACT_APP_FIREBASE_AUTH_DOMAIN` - Firebase auth domain
- `REACT_APP_FIREBASE_PROJECT_ID` - Firebase project ID
- `REACT_APP_FIREBASE_STORAGE_BUCKET` - Firebase storage bucket
- `REACT_APP_FIREBASE_MESSAGING_SENDER_ID` - Firebase messaging sender ID
- `REACT_APP_FIREBASE_APP_ID` - Firebase app ID

## Security Considerations

- Never commit `.env` files to version control
- Keep your API keys and credentials secure
- Use different API keys for development and production environments
- Consider using a secrets management solution for production deployments
