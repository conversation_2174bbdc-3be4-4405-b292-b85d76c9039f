#!/bin/bash

# <PERSON><PERSON>t to clean up the repository before pushing to GitHub

echo "Cleaning up repository for GitHub..."

# Remove service account JSON files
echo "Removing service account JSON files..."
find . -name "*.json" | grep -v "node_modules" | grep -v "package" | grep -v "manifest" | grep -v "cors-config" | xargs rm -f

# Create a backup of .env file
if [ -f .env ]; then
    echo "Creating backup of .env file..."
    cp .env .env.backup
    echo "Removing sensitive information from .env file..."
    # Create a clean .env file with placeholders
    cat > .env <<EOL
# RFP Navigator Environment Variables

# Google Cloud Project Settings
PROJECT_ID=your-project-id
REGION=us-central1
STAGING_BUCKET=gs://your-staging-bucket

# Google Cloud Authentication
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# API Keys
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
LANGCHAIN_API_KEY=your-langchain-api-key

# Weaviate Settings
WEAVIATE_URL=your-weaviate-url
WEAVIATE_API_KEY=your-weaviate-api-key

# Storage Buckets
RFP_TABLES_BUCKET=rfp_tables
RFP_WORKSHEET_BUCKET=rfp_worksheet

# CORS Settings
ALLOWED_ORIGINS=*

# Service URLs
QUERY_SERVICE_URL=https://query-service-url/query
FIRESTORE_API_URL=https://firestore-api-url/post_data
DELETE_SERVICE_URL=https://delete-service-url/delete
PREPROCESS_SERVICE_URL=https://preprocess-service-url/preprocess
EOL
fi

# Make sure .gitignore is properly set up
echo "Updating .gitignore file..."
cat > .gitignore <<EOL
# Environment variables
.env
.env.backup

# Google Cloud SDK
google-cloud-sdk/

# Service account keys and credentials
*.json
!package.json
!package-lock.json
!manifest.json
!cors-config.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
EOL

echo "Cleanup complete! You can now push to GitHub."
echo "Note: Your original .env file has been backed up as .env.backup"
echo "      Make sure to restore it after pushing to GitHub."
