# RFP Navigator Environment Variables
# This is a template .env file. Each microservice should have its own .env file
# with the appropriate values for that service.

# Google Cloud Project Settings
PROJECT_ID=your-project-id
REGION=us-central1
STAGING_BUCKET=gs://your-staging-bucket

# Google Cloud Authentication
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# API Keys
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
LANGCHAIN_API_KEY=your-langchain-api-key

# Weaviate Settings
WEAVIATE_URL=your-weaviate-url
WEAVIATE_API_KEY=your-weaviate-api-key

# Storage Buckets
RFP_TABLES_BUCKET=rfp_navigator_tables
RFP_WORKSHEET_BUCKET=rfp_navigator_worksheet

# CORS Settings
ALLOWED_ORIGINS=*

# Service URLs
QUERY_SERVICE_URL=https://query-service-url/query
FIRESTORE_API_URL=https://firestore-api-url
DELETE_SERVICE_URL=https://delete-service-url/delete
PREPROCESS_SERVICE_URL=https://preprocess-service-url/preprocess

# UI Settings (for rfp-navigator-ui/.env only)
REACT_APP_API_BASE_URL=https://api-service-url/
REACT_APP_QUERY_API=https://query-service-url
REACT_APP_SUGGESTIONS_API=https://firestore-api-url
REACT_APP_GET_DATA=https://firestore-api-url/get_data
REACT_APP_REANALYZE=https://reanalyze-service-url/reanalyze

# Firebase Configuration (for rfp-navigator-ui/.env only)
REACT_APP_FIREBASE_API_KEY=your-firebase-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project-id.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
