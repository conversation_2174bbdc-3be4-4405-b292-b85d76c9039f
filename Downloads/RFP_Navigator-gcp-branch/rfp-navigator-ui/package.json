{"name": "rfp-navigator-ui", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.0", "@mui/material": "^6.1.0", "@mui/styled-engine-sc": "^6.1.0", "@mui/x-data-grid": "^7.17.0", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "firebase": "^10.13.2", "formik": "^2.4.6", "react": "^18.3.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "redux-persist": "^6.0.0", "styled-components": "^6.1.13", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint 'src/**/*.{js,jsx}'", "lint:fix": "eslint 'src/**/*.{js,jsx}' --fix", "format": "prettier --write 'src/**/*.{js,jsx,css,md}'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.10.0", "babel-eslint": "^10.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.36.1", "globals": "^15.9.0", "prettier": "^3.3.3"}}