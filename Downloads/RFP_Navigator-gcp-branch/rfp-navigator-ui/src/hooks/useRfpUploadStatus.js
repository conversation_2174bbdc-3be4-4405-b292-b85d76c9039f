import { useState, useEffect } from "react";
import { getRfpUploadStatus } from "../apis/rfp";

export const useRfpUploadStatus = (rfpName) => {
  const [rfpUploadStatus, setrfpUploadStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRfpUploadStatus = async () => {
      try {
        const data = await getRfpUploadStatus(rfpName);
        setrfpUploadStatus(data);
      } catch (err) {
        setLoading(false);
        setError(err?.response?.data?.error || err.status);
      } finally {
        setLoading(false);
      }
    };

    fetchRfpUploadStatus();
  }, [rfpName]);

  return { rfpUploadStatus, loading, error };
};
