import { useState, useEffect } from "react";
import { getRfpDetails } from "../apis/rfp";

export const useRfpDetails = (rfpName, shouldFetchDetails) => {
  const [rfpDetails, setRfpDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchRfpDetails = async () => {
      try {
        const data = await getRfpDetails(rfpName);
        setRfpDetails(data);
      } catch (err) {
        setLoading(false);
        setError(err.response.data.error);
      } finally {
        setLoading(false);
      }
    };

    if (shouldFetchDetails) {
      fetchRfpDetails();
    }
  }, [rfpName, shouldFetchDetails]);

  return { rfpDetails, loading, error };
};
