import { useState, useEffect } from "react";
import { getSuggestions } from "../apis/rfp";

export const useSuggestions = (rfpName) => {
  const [suggestions, setSuggestions] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        const data = await getSuggestions(rfpName);
        setSuggestions(data);
      } catch (err) {
        setError(err.response.data.error);
      } finally {
        setLoading(false);
      }
    };

    fetchSuggestions();
  }, [rfpName]);

  return { suggestions, loading, error };
};
