import React from "react";

import { DataGrid } from "@mui/x-data-grid";
import Box from "@mui/material/Box";
import { Subtitle } from "../Subtitle";

export const columns = [{ field: "name", headerName: "Name", width: 150 }];

const UploadedFiles = ({ rows }) => {
  return (
    <Box pt={4}>
      <Subtitle text="Previously uploaded files from grid" />
      <DataGrid rows={rows} columns={columns} />
    </Box>
  );
};

export default UploadedFiles;
