import { Box, Link, Typography } from "@mui/material";
import React from "react";
import { useDispatch } from "react-redux";
import { setActiveTab } from "../../redux/slices/tabs";
import { useNavigate } from "react-router-dom";
import ConstructionIcon from "@mui/icons-material/Construction";

const UnderConstruction = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const handleOnClick = () => {
    dispatch(setActiveTab("upload"));
    navigate("/upload");
  };
  return (
    <Box sx={{ display: "flex", columnGap: 1, alignItems: "center" }}>
      <ConstructionIcon />
      <Typography variant="body2">
        This page isn’t ready yet. Head back to the{" "}
        <Link component="button" onClick={handleOnClick}>
          Home
        </Link>{" "}
        page while we finish up.
      </Typography>
    </Box>
  );
};

export default UnderConstruction;
