import { createSlice } from "@reduxjs/toolkit";

export const tabsSlice = createSlice({
  name: "tabs",
  initialState: {
    activeTab: "upload",
    rfpName: "",
  },
  reducers: {
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    setRfpName: (state, action) => {
      state.rfpName = action.payload;
    },
  },
});

export const { setActiveTab, setRfpName } = tabsSlice.actions;

export default tabsSlice.reducer;
