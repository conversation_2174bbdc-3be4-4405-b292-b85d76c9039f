import { getStorage, listAll, ref } from "firebase/storage";

export const getFilesFromStorage = (setFiles, setError, setLoading) => {
  const storage = getStorage();
  const storageRef = ref(storage, "uploads");

  listAll(storageRef)
    .then((res) => {
      setLoading(false);
      const files = res.items.map((item) => ({
        name: item.name,
        id: item.fullPath,
      }));
      setFiles(files);
      setError("");
    })
    .catch((error) => {
      setLoading(false);
      setError(error.code);
    });
};
