#!/bin/bash

# <PERSON><PERSON>t to push the code to GitHub

# Check if the repository is already initialized
if [ ! -d .git ]; then
    echo "Initializing Git repository..."
    git init
fi

# Run the cleanup script
echo "Running cleanup script..."
./cleanup_for_github.sh

# Add all files to Git
echo "Adding files to Git..."
git add .

# Commit the changes
echo "Committing changes..."
git commit -m "Initial commit of RFP Navigator"

# Add the GitHub remote
echo "Adding GitHub remote..."
git remote add origin https://github.com/Creo-DRB1008/RFP_Navigator.git

# Push to GitHub
echo "Pushing to GitHub..."
git stash save "Local changes before GitHub push"
git checkout -b gcp-branch
git push -u origin gcp-branch
git stash pop

# Restore the .env file
if [ -f .env.backup ]; then
    echo "Restoring .env file..."
    mv .env.backup .env
fi

echo "Push to GitHub complete!"
