# RFP Navigator

RFP Navigator is a system designed to analyze and extract information from Request for Proposal (RFP) documents, which are typically Excel files. The system uses AI and natural language processing to help users query and understand the content of these RFPs.

## Architecture

The system consists of several microservices:

1. **Firestore API**: Provides an interface to Google Firestore for storing and retrieving RFP data.
2. **Query Service**: Handles natural language queries about RFP documents.
3. **Preprocessing Service**: Processes Excel files containing RFP data.
4. **Reanalysis Service**: Allows reprocessing of RFP documents.

## Setup

### Prerequisites

- Python 3.8+
- Google Cloud Platform account
- Weaviate instance
- OpenAI API key (optional, for OpenAI models)
- Google API key (for Google Gemini models)

### Environment Variables

Create a `.env` file in each service directory with the required environment variables. See `.env.example` for a list of all required variables.

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/rfp-navigator.git
   cd rfp-navigator
   ```

2. Install dependencies for each service:
   ```bash
   cd query
   pip install -r requirements.txt

   cd ../firestore-api
   pip install -r requirements.txt

   # Repeat for other services
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## Usage

### Starting the Services

1. Start the Firestore API:
   ```bash
   cd firestore-api
   python app.py
   ```

2. Start the Query Service:
   ```bash
   cd query
   uvicorn app:app --reload
   ```

3. Start the Preprocessing Service:
   ```bash
   cd summary_cloud_run
   python main.py
   ```

### Processing an RFP

1. Upload an Excel file to Google Cloud Storage.
2. Call the preprocessing service to analyze the RFP.
3. Once processing is complete, you can query the RFP using the Query Service.

### Querying an RFP

Send a POST request to the Query Service:

```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is the scope of work?",
    "rfp_name": "example_rfp",
    "sql_agent": false,
    "open_ai": false
  }'
```

## Development

### Project Structure

```
rfp-navigator/
├── firestore-api/       # Firestore API service
├── query/               # Query service
│   ├── main/            # Core query functionality
│   ├── tests/           # Unit tests
│   └── app.py           # FastAPI application
├── delete/              # Delete service
│   ├── utils/           # Utility functions
│   ├── tests/           # Unit tests
│   └── main.py          # Flask application
├── summary_cloud_run/   # Preprocessing service
├── reanalyze/           # Reanalysis service
└── .github/workflows/   # CI/CD workflows
```

### Testing

Each microservice has its own test suite using pytest. To run tests for a specific service:

```bash
cd <service-directory>
python -m pytest
```

To run tests with coverage:

```bash
python -m pytest --cov=. --cov-report=term --cov-report=html
```

This will generate a coverage report in the terminal and an HTML report in the `coverage_html_report` directory.

### Continuous Integration

The project uses GitHub Actions for continuous integration. The CI pipeline:

1. Runs tests for all microservices
2. Generates coverage reports
3. Performs linting with flake8

The CI workflow is defined in `.github/workflows/ci.yml`.

### Adding New Features

1. Create a new branch for your feature.
2. Implement the feature.
3. Add tests to ensure code coverage.
4. Submit a pull request.

## Security

- All API keys and credentials should be stored as environment variables.
- CORS is configured to restrict access to specific origins.
- Input validation is implemented for all API endpoints.

## License

[Your License Here]
