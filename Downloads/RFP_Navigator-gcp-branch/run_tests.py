#!/usr/bin/env python3
"""
Script to run tests for all microservices and generate a combined coverage report.
"""

import os
import subprocess
import sys
import shutil

def run_command(command, cwd=None):
    """Run a command and return its output."""
    print(f"Running: {command} in {cwd or '.'}")
    result = subprocess.run(
        command,
        shell=True,
        cwd=cwd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)

    return result.returncode == 0

def main():
    """Run tests for all microservices and generate a combined coverage report."""
    # List of microservices to test
    microservices = ["delete", "query", "reanalyze", "firestore-api"]

    # Create a directory for the combined coverage report
    os.makedirs("coverage", exist_ok=True)

    # Run tests for each microservice
    success = True
    for service in microservices:
        print(f"\n{'=' * 80}\nRunning tests for {service}\n{'=' * 80}\n")

        # Run pytest with coverage - generate both database and XML files
        cmd = f"python -m pytest --cov=. --cov-report=xml:.coverage.{service}.xml --cov-report=term"
        if not run_command(cmd, cwd=service):
            print(f"Warning: Some tests failed for {service}, but continuing...")
            # Don't set success to False here, as we want to continue with other services

    # Combine coverage reports
    print(f"\n{'=' * 80}\nCombining coverage reports\n{'=' * 80}\n")
    # Copy .coverage files from each microservice directory
    coverage_files = []
    for service in microservices:
        coverage_file = ".coverage"
        if os.path.exists(os.path.join(service, coverage_file)):
            dest_file = f".coverage.{service}"
            shutil.copy(os.path.join(service, coverage_file), dest_file)
            coverage_files.append(dest_file)

    if coverage_files:
        print(f"Found coverage files: {coverage_files}")

        # Combine coverage reports
        if not run_command("coverage combine"):
            success = False
    else:
        print("No coverage files found to combine.")
        success = False

    # Generate combined coverage report
    print(f"\n{'=' * 80}\nGenerating combined coverage report\n{'=' * 80}\n")
    if not run_command("coverage report"):
        success = False

    if not run_command("coverage html -d coverage/html"):
        success = False

    if not run_command("coverage xml -o coverage.xml"):
        success = False

    print(f"\n{'=' * 80}\nTest Summary\n{'=' * 80}\n")
    if success:
        print("All tests passed!")
    else:
        print("Some tests failed. Check the output above for details.")

    print(f"\nCoverage report available at: {os.path.abspath('coverage/html/index.html')}")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
