"""
Pytest fixtures for the Firestore API service tests.
"""

import os
import pytest
from unittest.mock import patch, MagicMock
import sys
from flask import Flask
from flask.testing import Flask<PERSON>lient

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture
def mock_env_variables():
    """Fixture to mock environment variables required by the app."""
    env_vars = {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/credentials.json",
        "ALLOWED_ORIGINS": "*"
    }

    with patch.dict(os.environ, env_vars):
        yield env_vars

@pytest.fixture
def mock_firebase_admin():
    """Fixture to mock firebase_admin."""
    with patch('firebase_admin.initialize_app') as mock_init_app:
        mock_app = MagicMock()
        mock_init_app.return_value = mock_app
        yield mock_init_app, mock_app

@pytest.fixture
def mock_credentials():
    """Fixture to mock firebase_admin.credentials."""
    with patch('firebase_admin.credentials.Certificate') as mock_cert:
        yield mock_cert

@pytest.fixture
def mock_firestore_client():
    """Fixture to mock firestore.Client."""
    # Create mock objects
    mock_client = MagicMock()
    mock_collection = MagicMock()
    mock_doc = MagicMock()
    mock_doc_ref = MagicMock()

    # Configure the mock document reference with a string ID instead of a MagicMock
    mock_doc_ref.id = "test_doc_id"

    # Configure the mocks
    mock_client.collection.return_value = mock_collection
    mock_collection.stream.return_value = [mock_doc]
    mock_collection.add.return_value = (None, mock_doc_ref)

    mock_doc.to_dict.return_value = {"test_key": "test_value"}
    mock_doc.reference = MagicMock()

    # Patch the firestore.Client to return our mock client
    # This needs to be applied before the app is imported
    patcher = patch('firebase_admin.firestore.Client', return_value=mock_client)
    patcher.start()

    # Also patch the db instance directly in the app module
    # This ensures our tests use the mock even if the app has already initialized
    with patch.dict('sys.modules'):
        # Import the app module to get access to its namespace
        import app as app_module
        # Replace the db instance with our mock
        app_module.db = mock_client

    yield mock_client, mock_collection, mock_doc, mock_doc_ref

    # Stop the patcher after the test
    patcher.stop()

@pytest.fixture
def test_client(mock_env_variables, mock_firebase_admin, mock_credentials, mock_firestore_client):
    """Fixture to create a test client for the Flask app."""
    # Import the app here to ensure mocks are in place before app initialization
    from app import app

    # Configure the app for testing
    app.config['TESTING'] = True

    # Create a test client
    with app.test_client() as client:
        yield client
