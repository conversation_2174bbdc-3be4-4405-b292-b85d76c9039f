# Firestore API Tests

This directory contains unit tests for the Firestore API microservice.

## Test Structure

- `conftest.py` - Common pytest fixtures for all tests
- `test_app.py` - Tests for the Flask application

## Running Tests

To run all tests:

```bash
cd firestore-api
python -m pytest
```

To run tests with coverage:

```bash
cd firestore-api
python -m pytest --cov=. --cov-report=term --cov-report=html
```

This will generate a coverage report in the terminal and an HTML report in the `coverage_html_report` directory.

## Test Coverage

The tests aim to cover:

1. **Flask Application**
   - Endpoint functionality
   - Error handling
   - Request validation

2. **App Initialization**
   - Credentials loading
   - Firebase initialization
   - Error handling

## Mocking

The tests use mocks to avoid making actual API calls to:
- Firebase Admin
- Firestore

This ensures tests can run without external dependencies and are deterministic.
