<?xml version="1.0" ?>
<coverage version="7.8.0" timestamp="1746130019188" lines-valid="94" lines-covered="87" line-rate="0.9255" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.8.0 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Desktop/GCP/RFP Navigator/firestore-api</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9255" branch-rate="0" complexity="0">
			<classes>
				<class name="app.py" filename="app.py" complexity="0" line-rate="0.9255" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="45" hits="0"/>
						<line number="48" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="132" hits="1"/>
						<line number="135" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="178" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="184" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="191" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
