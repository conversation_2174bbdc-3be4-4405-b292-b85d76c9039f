"""
RFP Navigator Firestore API

This module provides a Flask application that serves as the interface to Firestore
for the RFP Navigator. It allows storing and retrieving data related to RFP documents.
"""

import json
import os
import logging
from google.cloud import firestore
from flask import Flask, request, jsonify
import firebase_admin
from firebase_admin import credentials
from firebase_admin import firestore
from flask_cors import CORS
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize Firebase with credentials from environment variable
cred_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
if not cred_path:
    logger.warning("GOOGLE_APPLICATION_CREDENTIALS environment variable not set. Using default credentials.")
    cred = None
else:
    logger.info(f"Using credentials from {cred_path}")
    try:
        cred = credentials.Certificate(cred_path)
    except FileNotFoundError:
        logger.error(f"Credentials file not found at {cred_path}. Using default credentials.")

# Initialize Firebase app
try:
    firebase_app = firebase_admin.initialize_app(cred)
    logger.info("Firebase app initialized successfully")
except Exception as e:
    logger.error(f"Error initializing Firebase app: {str(e)}")
    # For testing purposes, we'll continue without Firebase
    firebase_app = None

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
allowed_origins = os.getenv("ALLOWED_ORIGINS", "*").split(",")
logger.info(f"Configuring CORS with allowed origins: {allowed_origins}")
CORS(app, resources={r"/*": {"origins": allowed_origins}})

# Initialize Firestore client
db = firestore.Client()

# @app.route('/questions', methods=['GET'])
# def get_questions():
#     questions_ref = db.collection('questions')
#     docs = questions_ref.stream()

#     questions = [doc.to_dict() for doc in docs]
#     return jsonify(questions)

# @app.route('/questions', methods=['POST'])
# def add_question():
#     question_data = request.json

#     if 'question' not in question_data:
#         return jsonify({'error': 'Question is required'}), 400

#     questions_ref = db.collection('questions')
#     questions_ref.add({'question': question_data['question']})

#     return jsonify({'message': 'Question added successfully'}), 201

@app.route('/delete_collection/<string:rfp_name>', methods=['DELETE'])
def delete_collection(rfp_name):
    """
    Delete all documents in a Firestore collection.

    Args:
        rfp_name (str): The name of the RFP collection to delete.

    Returns:
        JSON response with success message.
    """
    logger.info(f"Deleting collection: {rfp_name}")

    try:
        # Get a reference to the Firestore collection
        collection_ref = db.collection(rfp_name)

        # Fetch all documents in the collection
        docs = collection_ref.stream()

        # Delete all documents in the collection
        count = 0
        for doc in docs:
            doc.reference.delete()
            count += 1

        logger.info(f"Deleted {count} documents from collection {rfp_name}")

        # After all documents are deleted, return a success message
        return jsonify({'message': f'Collection {rfp_name} deleted successfully', 'count': count}), 200

    except Exception as e:
        logger.error(f"Error deleting collection {rfp_name}: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/get_data/<string:rfp_name>', methods=['POST'])
def get_data(rfp_name):
    """
    Get data from a Firestore collection by key.

    Args:
        rfp_name (str): The name of the RFP collection to query.

    Returns:
        JSON response with the requested data.
    """
    try:
        data = request.json
        key = data.get('key')

        if not key:
            logger.warning(f"No key provided in request for {rfp_name}")
            return jsonify({'error': 'Key is required'}), 400

        logger.info(f"Getting data for key '{key}' from collection: {rfp_name}")

        # Access the collection
        collection_ref = db.collection(rfp_name)

        # Stream all documents and find the one that contains the unique key
        result = None
        for doc in collection_ref.stream():
            doc_data = doc.to_dict()
            if key in doc_data:
                result = doc_data
                break

        if result is None:
            logger.info(f"No data found for key '{key}' in collection {rfp_name}")
            if key == 'status':
                return jsonify(0)
            return jsonify({'error': f'No data found for key: {key}'}), 404

        logger.info(f"Data found for key '{key}' in collection {rfp_name}")
        return jsonify(result[key])

    except Exception as e:
        logger.error(f"Error getting data from {rfp_name}: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/post_data/<string:rfp_name>', methods=['POST'])
def add_data(rfp_name):
    """
    Add data to a Firestore collection.

    Args:
        rfp_name (str): The name of the RFP collection to add data to.

    Returns:
        JSON response with success message.
    """
    try:
        data = request.json
        key = data.get('key')
        value = data.get('value')

        if not key:
            logger.warning(f"No key provided in request for {rfp_name}")
            return jsonify({'error': 'Key is required'}), 400

        logger.info(f"Adding data for key '{key}' to collection: {rfp_name}")

        # Use rfp_name to access the collection
        collection_ref = db.collection(rfp_name)
        doc_ref = collection_ref.add({key: value})

        logger.info(f"Data added successfully for key '{key}' to collection {rfp_name}")

        # Get the document ID safely
        doc_id = "unknown"
        if doc_ref and len(doc_ref) > 1 and hasattr(doc_ref[1], 'id'):
            doc_id = doc_ref[1].id

        return jsonify({
            'message': 'Value added successfully',
            'document_id': doc_id
        }), 201

    except Exception as e:
        logger.error(f"Error adding data to {rfp_name}: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
