"""
Unit tests for the agent_core module.
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from main.agent_core import LangchainApp

class TestLangchainApp:
    @pytest.fixture
    def app_instance(self):
        """Fixture to create a LangchainApp instance with mocked dependencies."""
        with patch('vertexai.init'):
            app = LangchainApp(project="test-project", location="test-location", staging_bucket="test-bucket")
            return app

    def test_init(self):
        """Test LangchainApp initialization."""
        with patch('vertexai.init') as mock_init:
            app = LangchainApp(project="test-project", location="test-location", staging_bucket="test-bucket")

            # Assert that the attributes were set correctly
            assert app.project_id == "test-project"
            assert app.location == "test-location"

            # Assert that vertexai.init was called with the correct parameters
            mock_init.assert_called_once_with(project="test-project", location="test-location", staging_bucket="test-bucket")

    def test_download_file_from_gcs(self, app_instance):
        """Test download_file_from_gcs method."""
        with patch('main.agent_core.download_blob') as mock_download:
            app_instance.download_file_from_gcs("test-bucket", "source.txt", "dest.txt")

            # Assert that download_blob was called with the correct parameters
            mock_download.assert_called_once_with("test-bucket", "source.txt", "dest.txt")

    def test_set_up(self, app_instance):
        """Test set_up method."""
        with patch.dict(os.environ, {}, clear=True):
            app_instance.set_up()

            # Assert that the environment variables were set
            assert os.environ['LANGCHAIN_TRACING_V2'] == 'true'
            assert os.environ['LANGCHAIN_ENDPOINT'] == 'https://api.smith.langchain.com'
            assert os.environ['LANGCHAIN_API_KEY'] == ''

            # Assert that the prompt was created
            assert app_instance.prompt is not None

    def test_csv_agent(self, app_instance):
        """Test csv_agent method."""
        # Mock the pandas DataFrame
        mock_df = MagicMock()

        # Mock the agent
        mock_agent = MagicMock()
        mock_agent.invoke.return_value = {"output": "Test output"}

        # Mock the create_pandas_dataframe_agent function
        with patch('langchain_experimental.agents.create_pandas_dataframe_agent', return_value=mock_agent) as mock_create:
            # Set up the LLM handler
            app_instance.poly_llm = MagicMock()
            app_instance.poly_llm.chosen_llm = MagicMock()
            app_instance.poly_llm.chosen_llm.llm = "test-llm"

            # Call the method
            result = app_instance.csv_agent(mock_df, "Test question", "Test information")

            # Assert that create_pandas_dataframe_agent was called with the correct parameters
            mock_create.assert_called_once_with(
                llm="test-llm",
                df=mock_df,
                verbose=True,
                allow_dangerous_code=True
            )

            # Assert that agent.invoke was called with the correct parameters
            mock_agent.invoke.assert_called_once()
            args = mock_agent.invoke.call_args[0][0]
            assert "Test question" in args
            assert "Test information" in args

            # Assert that the method returned the expected result
            assert result == {"output": "Test output"}

    def test_table_search(self, app_instance):
        """Test table_search method."""
        # Mock the retrieved documents
        mock_doc1 = MagicMock()
        mock_doc1.metadata = {
            '_additional': {'score': 0.9},
            'text_as_html': 'test1.csv'
        }
        mock_doc1.page_content = "Test content 1"

        mock_doc2 = MagicMock()
        mock_doc2.metadata = {
            '_additional': {'score': 0.6},
            'text_as_html': 'test2.csv'
        }
        mock_doc2.page_content = "Test content 2"

        retrieved_docs = [mock_doc1, mock_doc2]

        # Mock the download_file_from_gcs method
        with patch.object(app_instance, 'download_file_from_gcs') as mock_download:
            # Mock pandas.read_csv
            with patch('pandas.read_csv') as mock_read_csv:
                # Mock the csv_agent method
                with patch.object(app_instance, 'csv_agent') as mock_csv_agent:
                    mock_csv_agent.return_value = {"output": "Test output"}

                    # Call the method
                    result = app_instance.table_search("Test query", "test_rfp", retrieved_docs)

                    # Assert that download_file_from_gcs was called with the correct parameters
                    mock_download.assert_called_once_with("rfp_navigator_tables", "test1.csv", "temp.csv")

                    # Assert that pandas.read_csv was called with the correct parameters
                    mock_read_csv.assert_called_once_with("temp.csv")

                    # Assert that csv_agent was called with the correct parameters
                    mock_csv_agent.assert_called_once()

                    # Assert that the method returned the expected result
                    assert result == ["Test output"]

    def test_table_search_no_results(self, app_instance):
        """Test table_search method when no documents meet the score threshold."""
        # Mock the retrieved documents
        mock_doc1 = MagicMock()
        mock_doc1.metadata = {
            '_additional': {'score': 0.9},
            'text_as_html': ''  # Empty source blob name
        }

        retrieved_docs = [mock_doc1]

        # Call the method
        result = app_instance.table_search("Test query", "test_rfp", retrieved_docs)

        # Assert that the method returned the expected result
        assert result == ["No relevant data found"]

    def test_query(self, app_instance):
        """Test query method."""
        # Set up environment variables
        with patch.dict(os.environ, {
            "WEAVIATE_URL": "https://test-url.com",
            "WEAVIATE_API_KEY": "test-key",
            "OPENAI_API_KEY": "test-key"
        }):
            # Mock the Weaviate client
            mock_client = MagicMock()
            with patch('weaviate.Client', return_value=mock_client) as mock_weaviate:
                # Mock the WeaviateHybridSearchRetriever
                mock_retriever = MagicMock()
                mock_chunk = MagicMock()
                mock_chunk.metadata = {
                    'worksheet': 'test.xlsx',
                    'excel_link': 'test-link'
                }
                mock_retriever.get_relevant_documents.return_value = [mock_chunk]

                with patch('main.agent_core.WeaviateHybridSearchRetriever', return_value=mock_retriever):
                    # Mock the LLMHandler
                    mock_llm_handler = MagicMock()
                    mock_llm_handler.process.return_value = "Test response"

                    with patch.object(LangchainApp, 'LLMHandler', return_value=mock_llm_handler):
                        # Mock the table_search method
                        with patch.object(app_instance, 'table_search', return_value=["Test table result"]):
                            # Mock the generate_unique_id function
                            with patch('main.agent_core.generate_unique_id', return_value="test-id"):
                                # Set up the prompt
                                app_instance.prompt = "test-prompt"

                                # Call the method
                                result = app_instance.query(
                                    question="Test question",
                                    rfp_name="test_rfp",
                                    sql_agent=False,
                                    open_ai=True
                                )

                                # Assert that the Weaviate client was created with the correct parameters
                                mock_weaviate.assert_called_once_with(
                                    url="https://test-url.com",
                                    auth_client_secret=mock_weaviate.call_args[1]['auth_client_secret'],
                                    additional_headers={"X-Openai-Api-Key": "test-key"}
                                )

                                # Assert that the retriever was used correctly
                                mock_retriever.get_relevant_documents.assert_called_once_with(
                                    "Test question",
                                    score=True,
                                    where_filter={
                                        "operator": "Equal",
                                        "path": ["excel"],
                                        "valueString": "test-id"
                                    }
                                )

                                # Assert that the LLMHandler was created with the correct parameters
                                assert app_instance.poly_llm == mock_llm_handler

                                # Assert that the method returned the expected result
                                assert result == ["Test response", ["test.xlsx"], ["test-link"]]

    def test_query_missing_weaviate_url(self, app_instance):
        """Test query method when WEAVIATE_URL is missing."""
        # Set up environment variables without WEAVIATE_URL
        with patch.dict(os.environ, {
            "WEAVIATE_API_KEY": "test-key",
            "OPENAI_API_KEY": "test-key"
        }, clear=True):
            # Call the method and expect a ValueError
            with pytest.raises(ValueError, match="WEAVIATE_URL environment variable not set or empty"):
                app_instance.query(
                    question="Test question",
                    rfp_name="test_rfp",
                    sql_agent=False,
                    open_ai=True
                )

    def test_query_missing_weaviate_api_key(self, app_instance):
        """Test query method when WEAVIATE_API_KEY is missing."""
        # Set up environment variables without WEAVIATE_API_KEY
        with patch.dict(os.environ, {
            "WEAVIATE_URL": "https://test-url.com",
            "OPENAI_API_KEY": "test-key"
        }, clear=True):
            # Call the method and expect a ValueError
            with pytest.raises(ValueError, match="WEAVIATE_API_KEY environment variable not set or empty"):
                app_instance.query(
                    question="Test question",
                    rfp_name="test_rfp",
                    sql_agent=False,
                    open_ai=True
                )

    def test_query_missing_openai_api_key(self, app_instance):
        """Test query method when OPENAI_API_KEY is missing."""
        # Set up environment variables without OPENAI_API_KEY
        with patch.dict(os.environ, {
            "WEAVIATE_URL": "https://test-url.com",
            "WEAVIATE_API_KEY": "test-key"
        }, clear=True):
            # Call the method and expect a ValueError
            with pytest.raises(ValueError, match="OPENAI_API_KEY environment variable not set or empty"):
                app_instance.query(
                    question="Test question",
                    rfp_name="test_rfp",
                    sql_agent=False,
                    open_ai=True
                )

class TestLLMHandler:
    def test_init_openai(self):
        """Test LLMHandler initialization with OpenAI."""
        # Mock the OpenAI class
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            with patch('main.agent_core.LangchainApp.LLMHandler.OpenAI') as mock_openai_class:
                # Create a mock instance
                mock_openai = MagicMock()
                mock_openai_class.return_value = mock_openai

                # Create the LLMHandler
                handler = LangchainApp.LLMHandler(open_ai=True, prompt="test-prompt")

                # Assert that the OpenAI class was used
                assert handler.chosen_llm == mock_openai

                # Assert that the OpenAI class was initialized with the correct parameters
                mock_openai_class.assert_called_once_with("test-prompt")

    def test_init_gemini(self):
        """Test LLMHandler initialization with Gemini."""
        # Mock the Gemini class
        with patch('main.agent_core.LangchainApp.LLMHandler.Gemini') as mock_gemini_class:
            # Create a mock instance
            mock_gemini = MagicMock()
            mock_gemini_class.return_value = mock_gemini

            # Create the LLMHandler
            handler = LangchainApp.LLMHandler(open_ai=False, prompt="test-prompt")

            # Assert that the Gemini class was used
            assert handler.chosen_llm == mock_gemini

            # Assert that the Gemini class was initialized with the correct parameters
            mock_gemini_class.assert_called_once_with("test-prompt", model_name="gemini-1.5-pro")

    def test_process(self):
        """Test LLMHandler process method."""
        # Create a mock LLM
        mock_llm = MagicMock()
        mock_llm.invoke_llm.return_value = "Test response"

        # Mock the environment variables
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            # Mock the OpenAI class
            with patch('langchain_openai.OpenAI'):
                # Create the LLMHandler with the mock LLM
                handler = LangchainApp.LLMHandler(open_ai=True, prompt="test-prompt")
                handler.chosen_llm = mock_llm

                # Call the process method
                result = handler.process(
                    chunks=["chunk1", "chunk2"],
                    possible_answer="possible answer",
                    question="test question"
                )

                # Assert that the LLM was used correctly
                mock_llm.invoke_llm.assert_called_once_with(
                    ["chunk1", "chunk2"],
                    "possible answer",
                    "test question"
                )

                # Assert that the method returned the expected result
                assert result == "Test response"
