"""
Pytest fixtures for the query service tests.
"""

import os
import pytest
from unittest.mock import patch, MagicMock
import sys
import json
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture
def mock_env_variables():
    """Fixture to mock environment variables required by the app."""
    env_vars = {
        "WEAVIATE_URL": "https://test-weaviate-url.com",
        "WEAVIATE_API_KEY": "test-weaviate-api-key",
        "OPENAI_API_KEY": "test-openai-api-key",
        "PROJECT_ID": "test-project-id",
        "REGION": "test-region",
        "STAGING_BUCKET": "test-staging-bucket",
        "RFP_WORKSHEET_BUCKET": "test-worksheet-bucket",
        "ALLOWED_ORIGINS": "*",
        "LANGCHAIN_API_KEY": "test-langchain-api-key",
        "GOOGLE_API_KEY": "test-google-api-key"
    }

    with patch.dict(os.environ, env_vars):
        yield env_vars

@pytest.fixture
def mock_weaviate_client():
    """Fixture to mock the Weaviate client."""
    mock_client = MagicMock()

    with patch('weaviate.Client', return_value=mock_client):
        yield mock_client

@pytest.fixture
def mock_storage_client():
    """Fixture to mock the Google Cloud Storage client."""
    mock_client = MagicMock()
    mock_bucket = MagicMock()
    mock_blob = MagicMock()

    mock_client.bucket.return_value = mock_bucket
    mock_bucket.blob.return_value = mock_blob
    mock_blob.generate_signed_url.return_value = "https://signed-url.com"

    # Mock the service_account.Credentials.from_service_account_file to avoid file not found error
    with patch('google.oauth2.service_account.Credentials.from_service_account_file'):
        # Mock the storage client
        with patch('google.cloud.storage.Client', return_value=mock_client):
            # Mock the get_storage_client function to return our mock client
            with patch('utils.storage.get_storage_client', return_value=mock_client):
                yield mock_client, mock_bucket, mock_blob

@pytest.fixture
def mock_langchain_app():
    """Fixture to mock the LangchainApp."""
    mock_app = MagicMock()
    mock_app.query.return_value = ["Test response", ["reference1.xlsx"], ["link1"]]

    with patch('main.agent_core.LangchainApp', return_value=mock_app):
        yield mock_app

@pytest.fixture
def mock_vertexai():
    """Fixture to mock VertexAI initialization."""
    with patch('vertexai.init') as mock_init:
        yield mock_init

@pytest.fixture
def mock_pandas():
    """Fixture to mock pandas."""
    mock_df = MagicMock()

    with patch('pandas.read_csv', return_value=mock_df):
        yield mock_df

@pytest.fixture
def test_client(mock_env_variables, mock_weaviate_client, mock_storage_client,
                mock_langchain_app, mock_vertexai, mock_pandas):
    """Fixture to create a test client for the FastAPI app."""
    # Import the app here to ensure mocks are in place before app initialization
    with patch('main.agent_core.LangchainApp.set_up'):
        from app import app

        # Create a test client
        client = TestClient(app)
        yield client
