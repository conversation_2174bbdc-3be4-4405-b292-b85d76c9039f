"""
Unit tests for the ID generator utility.
"""

import pytest
import hashlib
from utils.id_generator import generate_unique_id

def test_generate_unique_id_basic():
    """Test that generate_unique_id returns the expected hash for a simple input."""
    input_string = "test_rfp"
    expected_hash = hashlib.sha256(input_string.encode()).hexdigest()
    
    result = generate_unique_id(input_string)
    
    assert result == expected_hash
    assert len(result) == 64  # SHA-256 hash is 64 characters long

def test_generate_unique_id_normalization():
    """Test that generate_unique_id normalizes input by trimming whitespace and converting to lowercase."""
    # These should all generate the same hash
    inputs = [
        "test_rfp",
        "  test_rfp  ",
        "TEST_RFP",
        "  TEST_rfp  "
    ]
    
    # Get the expected hash from the normalized version
    expected_hash = hashlib.sha256("test_rfp".encode()).hexdigest()
    
    # Test each input
    for input_string in inputs:
        result = generate_unique_id(input_string)
        assert result == expected_hash

def test_generate_unique_id_empty_string():
    """Test that generate_unique_id handles empty strings correctly."""
    input_string = ""
    expected_hash = hashlib.sha256(input_string.encode()).hexdigest()
    
    result = generate_unique_id(input_string)
    
    assert result == expected_hash

def test_generate_unique_id_special_characters():
    """Test that generate_unique_id handles special characters correctly."""
    input_string = "test!@#$%^&*()_+"
    expected_hash = hashlib.sha256(input_string.encode()).hexdigest()
    
    result = generate_unique_id(input_string)
    
    assert result == expected_hash

def test_generate_unique_id_consistency():
    """Test that generate_unique_id returns consistent results for the same input."""
    input_string = "test_rfp"
    
    result1 = generate_unique_id(input_string)
    result2 = generate_unique_id(input_string)
    
    assert result1 == result2

def test_generate_unique_id_different_inputs():
    """Test that generate_unique_id returns different hashes for different inputs."""
    input1 = "test_rfp_1"
    input2 = "test_rfp_2"
    
    result1 = generate_unique_id(input1)
    result2 = generate_unique_id(input2)
    
    assert result1 != result2
