# Query Service Tests

This directory contains unit tests for the Query microservice.

## Test Structure

- `conftest.py` - Common pytest fixtures for all tests
- `test_app.py` - Tests for the FastAPI application
- `test_agent_core.py` - Tests for the LangchainApp and LLMHandler classes
- `test_id_generator.py` - Tests for the ID generator utility
- `test_storage.py` - Tests for the storage utility

## Running Tests

To run all tests:

```bash
cd query
python -m pytest
```

To run tests with coverage:

```bash
cd query
python -m pytest --cov=. --cov-report=term --cov-report=html
```

This will generate a coverage report in the terminal and an HTML report in the `coverage_html_report` directory.

## Test Coverage

The tests aim to cover:

1. **FastAPI Application**
   - Endpoint functionality
   - Error handling
   - Request validation

2. **LangchainApp**
   - Initialization
   - Query processing
   - Table search
   - Environment variable validation

3. **LLMHandler**
   - LLM selection (OpenAI vs. Gemini)
   - Query processing

4. **ID Generator Utility**
   - Hash generation
   - Input normalization
   - Edge cases

5. **Storage Utility**
   - Client initialization
   - File operations
   - URL generation

## Mocking

The tests use mocks to avoid making actual API calls to:
- Weaviate
- Google Cloud Storage
- OpenAI
- Vertex AI
- LangChain

This ensures tests can run without external dependencies and are deterministic.
