"""
Core functionality for the RFP Navigator query system.

This module provides the LangchainApp class which handles the core functionality
of querying RFP documents using LangChain and various LLM providers.
"""

import os
import logging
import vertexai
from langchain_core.prompts.prompt import PromptTemplate
from langchain_community.retrievers.weaviate_hybrid_search import WeaviateHybridSearchRetriever
import weaviate

# Import utility functions
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.id_generator import generate_unique_id
from utils.storage import download_blob

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LangchainApp:
    class LLMHandler:
        from abc import ABC, abstractmethod

        class LLM(ABC):
            # @abstractmethod
            def invoke_llm(self, chunks, possible_answer, question):
                pass

        class OpenAI(LLM):
            def __init__(self, prompt):
                from langchain_openai import OpenAI
                # Get OpenAI API key from environment variables
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if not openai_api_key:
                    logger.error("OPENAI_API_KEY environment variable not set or empty")
                    raise ValueError("OPENAI_API_KEY environment variable not set or empty")
                logger.debug(f"Using OpenAI API key: {openai_api_key[:5]}...")
                os.environ['OPENAI_API_KEY'] = openai_api_key
                self.llm = OpenAI()
                self.chain = prompt | self.llm

            def invoke_llm(self, chunks, possible_answer, question):
                return self.chain.invoke({
                    "chunks": chunks,
                    "possible_answer": possible_answer,
                    "query": question
                })

        class Gemini(LLM):
            def __init__(self, prompt, model_name):
                from langchain_google_genai import ChatGoogleGenerativeAI
                os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_API_KEY", "")
                self.llm = ChatGoogleGenerativeAI(model=model_name)
                self.chain = prompt | self.llm

            def invoke_llm(self, chunks, possible_answer, question):
                return self.chain.invoke({
                    "chunks": chunks,
                    "possible_answer": possible_answer,
                    "query": question
                }).content

        def __init__(self, open_ai: bool, prompt):
            if open_ai:
                self.chosen_llm = self.OpenAI(prompt)
            else:
                self.chosen_llm = self.Gemini(prompt, model_name="gemini-1.5-pro")

        def process(self, chunks, possible_answer, question):
            return self.chosen_llm.invoke_llm(chunks, possible_answer, question)

    def __init__(self, project: str, location: str, staging_bucket: str) -> None:
        self.project_id = project
        self.location = location
        vertexai.init(project=project, location=location, staging_bucket=staging_bucket)

    def download_file_from_gcs(self, bucket_name, source_blob_name, destination_file_name):
        """
        Download a file from Google Cloud Storage.

        Args:
            bucket_name (str): The name of the GCS bucket.
            source_blob_name (str): The path in GCS to the file to download.
            destination_file_name (str): The local path where the file should be saved.
        """
        logger.debug(f"Downloading {source_blob_name} from {bucket_name} to {destination_file_name}")
        download_blob(bucket_name, source_blob_name, destination_file_name)

    def csv_agent(self, df, QUESTION, Information):
        from langchain_experimental.agents import create_pandas_dataframe_agent
        agent = create_pandas_dataframe_agent(
            llm=self.poly_llm.chosen_llm.llm,
            df=df,
            verbose=True,
            allow_dangerous_code=True
        )

        CSV_PROMPT_PREFIX = """Ensure pandas shows all columns. Review data before answering."""
        CSV_PROMPT_SUFFIX = """Always double-check results with at least two methods. Don't guess."""

        return agent.invoke(
            CSV_PROMPT_PREFIX + "Information :" + Information + "QUESTION :" + QUESTION + CSV_PROMPT_SUFFIX
        )

    def table_search(self, query, rfp_name, retrieved_docs):
        import pandas as pd
        reply = []
        bucket_name = "rfp_navigator_tables"
        highest_score = retrieved_docs[0].metadata['_additional']['score']
        cut_off = float(highest_score) * 0.75

        for doc in retrieved_docs:
            if float(doc.metadata['_additional']['score']) >= cut_off:
                source_blob_name = doc.metadata['text_as_html']
                if source_blob_name.strip():
                    self.download_file_from_gcs(bucket_name, source_blob_name, 'temp.csv')
                    df = pd.read_csv('temp.csv')
                    agent = self.csv_agent(df, query, doc.page_content)
                    reply.append(agent["output"])
        return reply or ["No relevant data found"]

    def set_up(self):
        os.environ['LANGCHAIN_TRACING_V2'] = 'true'
        os.environ['LANGCHAIN_ENDPOINT'] = 'https://api.smith.langchain.com'
        os.environ['LANGCHAIN_API_KEY'] = os.getenv("LANGCHAIN_API_KEY", "")

        template = """Given these chunks: {chunks} and possible answer: {possible_answer}, answer this: {query} directly."""
        self.prompt = PromptTemplate(
            input_variables=["chunks", "possible_answer", "query"],
            template=template
        )

    def query(self, question: str, rfp_name: str, sql_agent: bool, open_ai: bool):
        """
        Query the RFP document with a natural language question.

        Args:
            question (str): The natural language question to ask.
            rfp_name (str): The name of the RFP document to query.
            sql_agent (bool): Whether to use SQL agent for querying tables.
            open_ai (bool): Whether to use OpenAI instead of Google's Gemini.

        Returns:
            list: A list containing [response, references, links].
        """
        logger.info(f"Querying RFP '{rfp_name}' with question: '{question}'")

        # Get API keys from environment variables
        weaviate_url = os.getenv("WEAVIATE_URL")
        weaviate_api_key = os.getenv("WEAVIATE_API_KEY")
        openai_api_key = os.getenv("OPENAI_API_KEY")

        # Validate API keys
        if not weaviate_url:
            logger.error("WEAVIATE_URL environment variable not set or empty")
            raise ValueError("WEAVIATE_URL environment variable not set or empty")

        if not weaviate_api_key:
            logger.error("WEAVIATE_API_KEY environment variable not set or empty")
            raise ValueError("WEAVIATE_API_KEY environment variable not set or empty")

        if not openai_api_key:
            logger.error("OPENAI_API_KEY environment variable not set or empty")
            raise ValueError("OPENAI_API_KEY environment variable not set or empty")

        logger.debug(f"Using Weaviate URL: {weaviate_url}")
        logger.debug(f"Using Weaviate API key: {weaviate_api_key[:3]}...")
        logger.debug(f"Using OpenAI API key: {openai_api_key[:5]}...")

        # Initialize Weaviate client
        client = weaviate.Client(
            url=weaviate_url,
            auth_client_secret=weaviate.AuthApiKey(api_key=weaviate_api_key),
            additional_headers={"X-Openai-Api-Key": openai_api_key}
        )

        # Generate unique ID for the RFP
        excel = generate_unique_id(rfp_name)
        logger.debug(f"Generated unique ID for RFP: {excel}")

        # Initialize the retriever
        self.retriever = WeaviateHybridSearchRetriever(
            alpha=0.65,
            client=client,
            index_name="RFP_RAG",
            text_key="page_content",
            k=2,
            attributes=["tags", "text_as_html", "excel", "excel_link", "worksheet"],
        )

        # Retrieve relevant documents
        logger.debug(f"Retrieving relevant documents for question")
        chunks = self.retriever.get_relevant_documents(
            question,
            score=True,
            where_filter={
                "operator": "Equal",
                "path": ["excel"],
                "valueString": excel
            }
        )
        logger.debug(f"Retrieved {len(chunks)} relevant chunks")

        # Initialize the LLM handler
        self.poly_llm = self.LLMHandler(open_ai, prompt=self.prompt)

        # Get possible answers from table search if not using SQL agent
        possible_answer = self.table_search(question, rfp_name, chunks) if not sql_agent else "none"

        # Extract references and links from chunks
        references = [chunk.metadata['worksheet'] for chunk in chunks]
        links = [chunk.metadata['excel_link'] for chunk in chunks]

        # Process the query with the LLM
        logger.debug(f"Processing query with LLM")
        response = self.poly_llm.process(chunks, possible_answer, question)

        logger.info(f"Query processed successfully")
        return [response, list(set(references)), list(set(links))]
