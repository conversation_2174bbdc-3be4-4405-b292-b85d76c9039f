"""
RFP Navigator Query Service

This module provides a FastAPI application that serves as the query interface for the RFP Navigator.
It allows users to query RFP documents using natural language and returns relevant information.
"""

from fastapi import FastAPI
from pydantic import BaseModel
from typing import Optional
import os
from main.agent_core import <PERSON>chain<PERSON><PERSON>
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import vertexai
import logging

# Import utility functions
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.storage import generate_signed_url

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="RFP Navigator Query API",
    description="API for querying RFP documents using natural language",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Get configuration from environment variables
PROJECT_ID = os.getenv("PROJECT_ID")
LOCATION = os.getenv("REGION")
STAGING_BUCKET = os.getenv("STAGING_BUCKET")
RFP_WORKSHEET_BUCKET = os.getenv("RFP_WORKSHEET_BUCKET")

# Initialize the LangchainApp
lang_app = LangchainApp(project=PROJECT_ID, location=LOCATION, staging_bucket=STAGING_BUCKET)
lang_app.set_up()

# Initialize VertexAI
vertexai.init(project=PROJECT_ID, location=LOCATION)
class QueryRequest(BaseModel):
    """
    Request model for querying RFP documents.

    Attributes:
        query (str): The natural language query to process.
        rfp_name (str): The name of the RFP document to query.
        sql_agent (bool, optional): Whether to use SQL agent for querying tables. Defaults to False.
        open_ai (bool, optional): Whether to use OpenAI instead of Google's Gemini. Defaults to False.
    """
    query: str
    rfp_name: str
    sql_agent: Optional[bool] = False
    open_ai: Optional[bool] = False

@app.post("/query", response_model=dict)
async def handle_query(payload: QueryRequest):
    """
    Handle a query request for an RFP document.

    This endpoint processes natural language queries against RFP documents and returns
    relevant information along with references to source documents.

    Args:
        payload (QueryRequest): The query request containing the query and RFP name.

    Returns:
        JSONResponse: A JSON response containing the query result and references.

    Raises:
        HTTPException: If an error occurs during query processing.
    """
    try:
        logger.info(f"Processing query: '{payload.query}' for RFP: '{payload.rfp_name}'")

        # Query the LangchainApp
        result = lang_app.query(
            question=payload.query,
            rfp_name=payload.rfp_name,
            sql_agent=payload.sql_agent,
            open_ai=payload.open_ai
        )

        # Get unique references
        unique = list(set(result[2]))
        references = []

        # Generate signed URLs for each reference
        for u in unique:
            logger.debug(f"Generating signed URL for: {u}")
            signed_url = generate_signed_url(RFP_WORKSHEET_BUCKET, u)
            references.append({
                "fileName": u,
                "fileUrl": signed_url
            })

        # Format the response
        response = {
            "data": result[0],
            "references": references
        }

        logger.info(f"Query processed successfully")
        return JSONResponse(content=response)

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}", exc_info=True)
        return {"error": str(e)}
