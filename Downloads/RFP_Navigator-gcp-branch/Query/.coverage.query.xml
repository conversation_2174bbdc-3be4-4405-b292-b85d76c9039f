<?xml version="1.0" ?>
<coverage version="7.8.0" timestamp="1746130017365" lines-valid="189" lines-covered="179" line-rate="0.9471" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.8.0 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Desktop/GCP/RFP Navigator/query</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9375" branch-rate="0" complexity="0">
			<classes>
				<class name="app.py" filename="app.py" complexity="0" line-rate="0.9375" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="40" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="117" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="main" line-rate="0.9346" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="main/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="agent_core.py" filename="main/agent_core.py" complexity="0" line-rate="0.9346" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="0"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="0"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="63" hits="1"/>
						<line number="64" hits="0"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="84" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="108" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="140" hits="1"/>
						<line number="153" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="178" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="189" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="209" hits="1"/>
						<line number="212" hits="1"/>
						<line number="215" hits="1"/>
						<line number="218" hits="1"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="1"/>
						<line number="225" hits="1"/>
						<line number="226" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="utils" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="utils/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="id_generator.py" filename="utils/id_generator.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
					</lines>
				</class>
				<class name="storage.py" filename="utils/storage.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="99" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="132" hits="1"/>
						<line number="137" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
