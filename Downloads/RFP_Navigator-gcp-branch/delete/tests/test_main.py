"""
Unit tests for the main Flask application.
"""

import pytest
import os
import json
from unittest.mock import patch, MagicMock, call
import requests
import sys
from flask import Flask

# Import the delete_files_in_folder function directly for testing
from main import delete_files_in_folder

def create_app_with_env(env_vars):
    """
    Helper function to create a Flask app with specific environment variables.

    Args:
        env_vars (dict): Dictionary of environment variables to set.

    Returns:
        Flask app or raises an exception if initialization fails.
    """
    # Save the original environment variables
    original_env = os.environ.copy()

    try:
        # Clear environment and set only the specified variables
        os.environ.clear()
        os.environ.update(env_vars)

        # Create a mock Flask app
        mock_app = MagicMock(spec=Flask)

        # Mock the app initialization to test environment variable validation
        with patch('flask.Flask', return_value=mock_app):
            # Import the app module fresh
            if 'main' in sys.modules:
                del sys.modules['main']

            # Try to import the app - this should validate environment variables
            import main
            return main.app

    finally:
        # Restore the original environment variables
        os.environ.clear()
        os.environ.update(original_env)

class TestDeleteFilesInFolder:
    def test_delete_files_in_folder_success(self, mock_storage_client):
        """Test delete_files_in_folder when successful."""
        mock_client, mock_bucket, mock_blob = mock_storage_client

        # Call the function
        result = delete_files_in_folder("test-bucket", "test-folder/")

        # Assert that the storage client was used correctly
        mock_client.bucket.assert_called_once_with("test-bucket")
        mock_bucket.list_blobs.assert_called_once_with(prefix="test-folder/")

        # Assert that delete was called for each blob
        assert mock_blob.delete.call_count == 2

        # Assert that the function returned the correct count
        assert result == 2

    def test_delete_files_in_folder_empty(self, mock_storage_client):
        """Test delete_files_in_folder when no files are found."""
        mock_client, mock_bucket, _ = mock_storage_client

        # Make list_blobs return an empty list
        mock_bucket.list_blobs.return_value = []

        # Call the function
        result = delete_files_in_folder("test-bucket", "empty-folder/")

        # Assert that the storage client was used correctly
        mock_client.bucket.assert_called_once_with("test-bucket")
        mock_bucket.list_blobs.assert_called_once_with(prefix="empty-folder/")

        # Assert that the function returned 0
        assert result == 0

    def test_delete_files_in_folder_error(self, mock_storage_client):
        """Test delete_files_in_folder when an error occurs."""
        mock_client, mock_bucket, _ = mock_storage_client

        # Make list_blobs raise an exception
        mock_bucket.list_blobs.side_effect = Exception("Test error")

        # Call the function and expect an exception
        with pytest.raises(Exception, match="Test error"):
            delete_files_in_folder("test-bucket", "test-folder/")

        # Assert that the storage client was used correctly
        mock_client.bucket.assert_called_once_with("test-bucket")
        mock_bucket.list_blobs.assert_called_once_with(prefix="test-folder/")

class TestDeleteEndpoint:
    def test_delete_success(self, test_client, mock_requests, mock_storage_client):
        """Test the delete endpoint when all operations are successful."""
        mock_delete, mock_response = mock_requests
        mock_client, mock_bucket, _ = mock_storage_client

        # Set up the mock response
        mock_response.status_code = 200

        # Call the endpoint
        response = test_client.delete('/delete/test_rfp')

        # Assert that the response is correct
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["message"] == "RFP test_rfp deleted successfully"
        assert data["rfp_name"] == "test_rfp"

        # Assert that the Firestore API was called
        mock_delete.assert_called_once()

        # Assert that the storage buckets were accessed
        assert mock_client.bucket.call_count == 2
        assert mock_bucket.list_blobs.call_count == 2

    def test_delete_firestore_error(self, test_client, mock_requests):
        """Test the delete endpoint when Firestore deletion fails."""
        mock_delete, _ = mock_requests

        # Make the request raise an exception
        mock_delete.side_effect = requests.exceptions.RequestException("Firestore error")

        # Call the endpoint
        response = test_client.delete('/delete/test_rfp')

        # Assert that the response indicates an error
        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
        assert "Firestore error" in data["error"]

    def test_delete_weaviate_error(self, test_client):
        """Test the delete endpoint when Weaviate deletion fails."""
        # Create a patch for the batch.delete_objects method that will raise an exception
        with patch('main.client.batch.delete_objects', side_effect=Exception("Weaviate error")):
            # Call the endpoint
            response = test_client.delete('/delete/test_rfp')

            # Assert that the response indicates an error
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "Weaviate error" in data["error"]

    def test_delete_storage_error(self, test_client, mock_storage_client):
        """Test the delete endpoint when storage deletion fails."""
        _, mock_bucket, _ = mock_storage_client

        # Make list_blobs raise an exception
        mock_bucket.list_blobs.side_effect = Exception("Storage error")

        # Call the endpoint
        response = test_client.delete('/delete/test_rfp')

        # Assert that the response indicates an error
        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
        assert "Storage error" in data["error"]



class TestAppInitialization:
    def test_app_initialization_with_env_vars(self):
        """Test that the app initializes correctly with environment variables."""
        # Set up the environment variables
        env_vars = {
            "WEAVIATE_URL": "https://test-url.com",
            "WEAVIATE_API_KEY": "test-key",
            "OPENAI_API_KEY": "test-key",
            "FIRESTORE_API_URL": "https://test-url.com",
            "RFP_TABLES_BUCKET": "test-bucket",
            "RFP_WORKSHEET_BUCKET": "test-bucket"
        }

        # Mock the Weaviate client directly
        mock_client = MagicMock()

        # Use a context manager to patch the Weaviate client
        with patch.dict(os.environ, env_vars):
            with patch('weaviate.Client', return_value=mock_client):
                # Import the app module fresh
                if 'main' in sys.modules:
                    del sys.modules['main']

                # Import the app
                import main

                # Assert that the app was created
                assert isinstance(main.app, Flask)

    def test_app_initialization_missing_weaviate_url(self):
        """Test app initialization when WEAVIATE_URL is missing."""
        # Set up the environment variables without WEAVIATE_URL
        env_vars = {
            "WEAVIATE_API_KEY": "test-key",
            "OPENAI_API_KEY": "test-key",
            "FIRESTORE_API_URL": "https://test-url.com",
            "RFP_TABLES_BUCKET": "test-bucket",
            "RFP_WORKSHEET_BUCKET": "test-bucket"
        }

        # Mock the validation function to raise the expected error
        with patch('main.load_dotenv'):
            with patch('os.getenv') as mock_getenv:
                # Make getenv return None for WEAVIATE_URL but values for other keys
                def mock_getenv_side_effect(key, default=None):
                    if key == "WEAVIATE_URL":
                        return None
                    return "test-value"

                mock_getenv.side_effect = mock_getenv_side_effect

                # Try to create the app and expect a ValueError
                with pytest.raises(ValueError, match="WEAVIATE_URL environment variable not set or empty"):
                    # Import the app module fresh to trigger validation
                    if 'main' in sys.modules:
                        del sys.modules['main']
                    import main

    def test_app_initialization_missing_weaviate_api_key(self):
        """Test app initialization when WEAVIATE_API_KEY is missing."""
        # Mock the validation function to raise the expected error
        with patch('main.load_dotenv'):
            with patch('os.getenv') as mock_getenv:
                # Make getenv return None for WEAVIATE_API_KEY but values for other keys
                def mock_getenv_side_effect(key, default=None):
                    if key == "WEAVIATE_API_KEY":
                        return None
                    return "test-value"

                mock_getenv.side_effect = mock_getenv_side_effect

                # Try to create the app and expect a ValueError
                with pytest.raises(ValueError, match="WEAVIATE_API_KEY environment variable not set or empty"):
                    # Import the app module fresh to trigger validation
                    if 'main' in sys.modules:
                        del sys.modules['main']
                    import main

    def test_app_initialization_missing_openai_api_key(self):
        """Test app initialization when OPENAI_API_KEY is missing."""
        # Mock the validation function to raise the expected error
        with patch('main.load_dotenv'):
            with patch('os.getenv') as mock_getenv:
                # Make getenv return None for OPENAI_API_KEY but values for other keys
                def mock_getenv_side_effect(key, default=None):
                    if key == "OPENAI_API_KEY":
                        return None
                    return "test-value"

                mock_getenv.side_effect = mock_getenv_side_effect

                # Try to create the app and expect a ValueError
                with pytest.raises(ValueError, match="OPENAI_API_KEY environment variable not set or empty"):
                    # Import the app module fresh to trigger validation
                    if 'main' in sys.modules:
                        del sys.modules['main']
                    import main
