"""
Pytest fixtures for the delete service tests.
"""

import os
import pytest
from unittest.mock import patch, MagicMock
import sys
import json
from flask import Flask

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture
def mock_env_variables():
    """Fixture to mock environment variables required by the app."""
    env_vars = {
        "WEAVIATE_URL": "https://test-weaviate-url.com",
        "WEAVIATE_API_KEY": "test-weaviate-api-key",
        "OPENAI_API_KEY": "test-openai-api-key",
        "FIRESTORE_API_URL": "https://test-firestore-api-url.com",
        "RFP_TABLES_BUCKET": "test-tables-bucket",
        "RFP_WORKSHEET_BUCKET": "test-worksheet-bucket",
        "ALLOWED_ORIGINS": "*"
    }

    with patch.dict(os.environ, env_vars):
        yield env_vars

@pytest.fixture
def mock_weaviate_client():
    """Fixture to mock the Weaviate client."""
    mock_client = MagicMock()
    mock_batch = MagicMock()
    mock_client.batch = mock_batch

    with patch('weaviate.Client', return_value=mock_client):
        yield mock_client

@pytest.fixture
def mock_storage_client():
    """Fixture to mock the Google Cloud Storage client."""
    mock_client = MagicMock()
    mock_bucket = MagicMock()
    mock_blob = MagicMock()

    mock_client.bucket.return_value = mock_bucket
    mock_bucket.blob.return_value = mock_blob
    mock_bucket.list_blobs.return_value = [mock_blob, mock_blob]  # Return two mock blobs

    with patch('google.cloud.storage.Client', return_value=mock_client):
        yield mock_client, mock_bucket, mock_blob

@pytest.fixture
def mock_requests():
    """Fixture to mock requests library."""
    with patch('requests.delete') as mock_delete:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_delete.return_value = mock_response
        yield mock_delete, mock_response

@pytest.fixture
def test_client(mock_env_variables, mock_weaviate_client, mock_storage_client, mock_requests):
    """Fixture to create a test client for the Flask app."""
    # Import the app here to ensure mocks are in place before app initialization
    with patch('weaviate.Client', return_value=mock_weaviate_client):
        from main import app

        # Configure the app for testing
        app.config['TESTING'] = True

        # Create a test client
        with app.test_client() as client:
            yield client
