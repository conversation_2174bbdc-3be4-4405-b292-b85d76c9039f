"""
Unit tests for the storage utility.
"""

import pytest
import os
import datetime
from unittest.mock import patch, MagicMock
from utils.storage import get_storage_client, upload_blob, download_blob, generate_signed_url
from google.cloud import storage
from google.oauth2 import service_account

class TestGetStorageClient:
    def test_get_storage_client_with_credentials(self):
        """Test get_storage_client when credentials are provided."""
        # Mock environment variable
        with patch.dict(os.environ, {"TEST_CREDENTIALS": "/path/to/credentials.json"}):
            # Mock service_account.Credentials.from_service_account_file
            with patch('google.oauth2.service_account.Credentials.from_service_account_file') as mock_creds:
                # Mock storage.Client
                with patch('google.cloud.storage.Client') as mock_client:
                    # Call the function
                    client = get_storage_client("TEST_CREDENTIALS")
                    
                    # Assert that the credentials were loaded
                    mock_creds.assert_called_once_with("/path/to/credentials.json")
                    
                    # Assert that the client was created with the credentials
                    mock_client.assert_called_once()
    
    def test_get_storage_client_without_credentials(self):
        """Test get_storage_client when no credentials are provided."""
        # Ensure the environment variable is not set
        with patch.dict(os.environ, {}, clear=True):
            # Mock storage.Client
            with patch('google.cloud.storage.Client') as mock_client:
                # Call the function
                client = get_storage_client()
                
                # Assert that the client was created without credentials
                mock_client.assert_called_once_with()

class TestUploadBlob:
    def test_upload_blob(self):
        """Test upload_blob function."""
        # Mock the storage client
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        # Mock get_storage_client to return our mock client
        with patch('utils.storage.get_storage_client', return_value=mock_client):
            # Mock print to capture output
            with patch('builtins.print') as mock_print:
                # Call the function
                result = upload_blob("test-bucket", "local_file.txt", "remote_file.txt")
                
                # Assert that the client was used correctly
                mock_client.bucket.assert_called_once_with("test-bucket")
                mock_bucket.blob.assert_called_once_with("remote_file.txt")
                mock_blob.upload_from_filename.assert_called_once_with("local_file.txt")
                
                # Assert that the function printed the expected message
                mock_print.assert_called_once_with("File local_file.txt uploaded to remote_file.txt.")
                
                # Assert that the function returned the expected URL
                assert result == "gs://test-bucket/remote_file.txt"

class TestDownloadBlob:
    def test_download_blob(self):
        """Test download_blob function."""
        # Mock the storage client
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        # Mock get_storage_client to return our mock client
        with patch('utils.storage.get_storage_client', return_value=mock_client):
            # Mock print to capture output
            with patch('builtins.print') as mock_print:
                # Call the function
                download_blob("test-bucket", "remote_file.txt", "local_file.txt")
                
                # Assert that the client was used correctly
                mock_client.bucket.assert_called_once_with("test-bucket")
                mock_bucket.blob.assert_called_once_with("remote_file.txt")
                mock_blob.download_to_filename.assert_called_once_with("local_file.txt")
                
                # Assert that the function printed the expected message
                mock_print.assert_called_once_with("File remote_file.txt downloaded to local_file.txt.")

class TestGenerateSignedUrl:
    def test_generate_signed_url(self):
        """Test generate_signed_url function."""
        # Mock the storage client
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.generate_signed_url.return_value = "https://signed-url.com"
        
        # Mock get_storage_client to return our mock client
        with patch('utils.storage.get_storage_client', return_value=mock_client):
            # Call the function
            result = generate_signed_url("test-bucket", "test-blob", 7200)
            
            # Assert that the client was used correctly
            mock_client.bucket.assert_called_once_with("test-bucket")
            mock_bucket.blob.assert_called_once_with("test-blob")
            
            # Assert that generate_signed_url was called with the correct parameters
            mock_blob.generate_signed_url.assert_called_once()
            args, kwargs = mock_blob.generate_signed_url.call_args
            assert kwargs["method"] == "GET"
            assert isinstance(kwargs["expiration"], datetime.timedelta)
            assert kwargs["expiration"].total_seconds() == 7200
            
            # Assert that the function returned the expected URL
            assert result == "https://signed-url.com"
    
    def test_generate_signed_url_default_expiration(self):
        """Test generate_signed_url function with default expiration."""
        # Mock the storage client
        mock_client = MagicMock()
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.generate_signed_url.return_value = "https://signed-url.com"
        
        # Mock get_storage_client to return our mock client
        with patch('utils.storage.get_storage_client', return_value=mock_client):
            # Call the function with default expiration
            result = generate_signed_url("test-bucket", "test-blob")
            
            # Assert that generate_signed_url was called with the correct parameters
            mock_blob.generate_signed_url.assert_called_once()
            args, kwargs = mock_blob.generate_signed_url.call_args
            assert kwargs["method"] == "GET"
            assert isinstance(kwargs["expiration"], datetime.timedelta)
            assert kwargs["expiration"].total_seconds() == 3600  # Default is 1 hour
