# Delete Service Tests

This directory contains unit tests for the Delete microservice.

## Test Structure

- `conftest.py` - Common pytest fixtures for all tests
- `test_main.py` - Tests for the main Flask application
- `test_id_generator.py` - Tests for the ID generator utility
- `test_storage.py` - Tests for the storage utility

## Running Tests

To run all tests:

```bash
cd delete
python -m pytest
```

To run tests with coverage:

```bash
cd delete
python -m pytest --cov=. --cov-report=term --cov-report=html
```

This will generate a coverage report in the terminal and an HTML report in the `coverage_html_report` directory.

## Test Coverage

The tests aim to cover:

1. **Main Flask Application**
   - Endpoint functionality
   - Error handling
   - Environment variable validation

2. **ID Generator Utility**
   - Hash generation
   - Input normalization
   - Edge cases

3. **Storage Utility**
   - Client initialization
   - File operations
   - URL generation

## Mocking

The tests use mocks to avoid making actual API calls to:
- Weaviate
- Google Cloud Storage
- Firestore API

This ensures tests can run without external dependencies and are deterministic.
