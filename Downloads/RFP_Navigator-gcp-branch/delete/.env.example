# Google Cloud Project Settings
PROJECT_ID=your-project-id
REGION=us-central1
STAGING_BUCKET=gs://your-staging-bucket

# Google Cloud Authentication
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# API Keys
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
LANGCHAIN_API_KEY=your-langchain-api-key

# Weaviate Settings
WEAVIATE_URL=your-weaviate-url
WEAVIATE_API_KEY=your-weaviate-api-key

# Storage Buckets
RFP_TABLES_BUCKET=rfp_navigator_tables
RFP_WORKSHEET_BUCKET=rfp_navigator_worksheet

# CORS Settings
ALLOWED_ORIGINS=*

# Service URLs
QUERY_SERVICE_URL=https://query-service-url/query
FIRESTORE_API_URL=https://firestore-api-url
DELETE_SERVICE_URL=https://delete-service-url/delete
PREPROCESS_SERVICE_URL=https://preprocess-service-url/preprocess
