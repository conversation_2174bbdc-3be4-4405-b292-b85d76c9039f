<?xml version="1.0" ?>
<coverage version="7.8.0" timestamp="1746130013227" lines-valid="123" lines-covered="118" line-rate="0.9593" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.8.0 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Desktop/GCP/RFP Navigator/delete</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9438" branch-rate="0" complexity="0">
			<classes>
				<class name="main.py" filename="main.py" complexity="0" line-rate="0.9438" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="83" hits="1"/>
						<line number="85" hits="1"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="94" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="124" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="140" hits="0"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="151" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="utils" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="utils/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="id_generator.py" filename="utils/id_generator.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
					</lines>
				</class>
				<class name="storage.py" filename="utils/storage.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="75" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="99" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="132" hits="1"/>
						<line number="137" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
