"""
RFP Navigator Delete Service

This module provides a Flask application that serves as the delete service for the RFP Navigator.
It allows deleting RFP documents from Firestore, Weaviate, and Google Cloud Storage.
"""

import os
import logging
from flask_cors import CORS
from flask import Flask, jsonify
import requests
import weaviate
from google.cloud import storage
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
allowed_origins = os.getenv("ALLOWED_ORIGINS", "*").split(",")
logger.info(f"Configuring CORS with allowed origins: {allowed_origins}")
CORS(app, resources={r"/*": {"origins": allowed_origins}})

# Get API keys from environment variables
weaviate_url = os.getenv("WEAVIATE_URL")
weaviate_api_key = os.getenv("WEAVIATE_API_KEY")
openai_api_key = os.getenv("OPENAI_API_KEY")

# Validate API keys
if not weaviate_url:
    logger.error("WEAVIATE_URL environment variable not set or empty")
    raise ValueError("WEAVIATE_URL environment variable not set or empty")

if not weaviate_api_key:
    logger.error("WEAVIATE_API_KEY environment variable not set or empty")
    raise ValueError("WEAVIATE_API_KEY environment variable not set or empty")

if not openai_api_key:
    logger.error("OPENAI_API_KEY environment variable not set or empty")
    raise ValueError("OPENAI_API_KEY environment variable not set or empty")

logger.debug(f"Using Weaviate URL: {weaviate_url}")
logger.debug(f"Using Weaviate API key: {weaviate_api_key[:3]}...")
logger.debug(f"Using OpenAI API key: {openai_api_key[:5]}...")

# Initialize Weaviate client
auth_config = weaviate.AuthApiKey(api_key=weaviate_api_key)
client = weaviate.Client(
    url=weaviate_url,
    auth_client_secret=auth_config,
    additional_headers={
        "X-Openai-Api-Key": openai_api_key,
    }
)

# Add path to parent directory for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions
from utils.id_generator import generate_unique_id

def delete_files_in_folder(bucket_name, folder_path):
    """
    Deletes all files in a folder in the GCS bucket.

    Args:
        bucket_name (str): The name of the GCS bucket.
        folder_path (str): The path to the folder in the bucket.

    Returns:
        int: The number of files deleted.
    """
    try:
        # Initialize the storage client
        storage_client = storage.Client()

        # Reference the bucket
        bucket = storage_client.bucket(bucket_name)

        # List all files with the given folder path (prefix)
        blobs = bucket.list_blobs(prefix=folder_path)

        # Count deleted files
        count = 0

        # Iterate through all the blobs (files) and delete them
        for blob in blobs:
            logger.info(f"Deleting blob: {blob.name} from bucket: {bucket_name}")
            blob.delete()  # Delete the file
            logger.info(f"Blob {blob.name} deleted.")
            count += 1

        return count
    except Exception as e:
        logger.error(f"Error deleting files from {bucket_name}/{folder_path}: {str(e)}", exc_info=True)
        raise

@app.route('/delete/<string:rfp_name>', methods=['DELETE'])
def delete(rfp_name):
    """
    Delete an RFP document and all associated data.

    This endpoint deletes:
    1. The Firestore collection for the RFP
    2. The Weaviate objects for the RFP
    3. The files in Google Cloud Storage for the RFP

    Args:
        rfp_name (str): The name of the RFP to delete.

    Returns:
        JSON response with deletion status.
    """
    logger.info(f"Deleting RFP: {rfp_name}")

    # Get the Firestore API URL from environment variables
    firestore_api_url = os.getenv("FIRESTORE_API_URL")
    if not firestore_api_url:
        logger.error("FIRESTORE_API_URL environment variable not set or empty")
        return jsonify({"error": "FIRESTORE_API_URL environment variable not set or empty"}), 500
    delete_collection_url = f"{firestore_api_url}/delete_collection/{rfp_name}"

    # Step 1: Delete from Firestore
    try:
        logger.info(f"Deleting Firestore collection: {rfp_name}")
        response = requests.delete(delete_collection_url)
        if response.status_code == 200:
            logger.info(f"Firestore collection {rfp_name} deleted successfully")
        else:
            logger.warning(f"Firestore deletion returned status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error deleting Firestore collection: {str(e)}", exc_info=True)
        return jsonify({"error": f"Error deleting Firestore collection: {str(e)}"}), 500

    # Step 2: Delete from Weaviate
    try:
        logger.info(f"Deleting Weaviate objects for RFP: {rfp_name}")
        excel = generate_unique_id(rfp_name)
        logger.debug(f"Generated unique ID for RFP: {excel}")

        client.batch.delete_objects(
            class_name="RFP_RAG",
            where={
                "operator": "Equal",
                "path": ["excel"],
                "valueString": excel
            },
        )
        logger.info(f"Weaviate objects for RFP {rfp_name} deleted successfully")
    except Exception as e:
        logger.error(f"Error deleting Weaviate objects: {str(e)}", exc_info=True)
        return jsonify({"error": f"Error deleting Weaviate objects: {str(e)}"}), 500

    # Step 3: Delete from Google Cloud Storage
    try:
        logger.info(f"Deleting files from Google Cloud Storage for RFP: {rfp_name}")

        # Get bucket names from environment variables
        tables_bucket = os.getenv("RFP_TABLES_BUCKET")
        worksheet_bucket = os.getenv("RFP_WORKSHEET_BUCKET")

        if not tables_bucket or not worksheet_bucket:
            logger.error("RFP_TABLES_BUCKET or RFP_WORKSHEET_BUCKET environment variable not set or empty")
            return jsonify({"error": "Storage bucket environment variables not set or empty"}), 500

        # Delete files from both buckets
        tables_count = delete_files_in_folder(tables_bucket, rfp_name + "/")
        worksheet_count = delete_files_in_folder(worksheet_bucket, rfp_name + "/")

        logger.info(f"Deleted {tables_count} files from {tables_bucket} and {worksheet_count} files from {worksheet_bucket}")
    except Exception as e:
        logger.error(f"Error deleting files from Google Cloud Storage: {str(e)}", exc_info=True)
        return jsonify({"error": f"Error deleting files from Google Cloud Storage: {str(e)}"}), 500

    logger.info(f"RFP {rfp_name} deleted successfully")
    return jsonify({
        "message": f"RFP {rfp_name} deleted successfully",
        "rfp_name": rfp_name
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)