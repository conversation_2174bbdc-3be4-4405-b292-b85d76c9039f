# Reanalyze Service Tests

This directory contains unit tests for the Reanalyze microservice.

## Test Structure

- `conftest.py` - Common pytest fixtures for all tests
- `test_main.py` - Tests for the Flask application

## Running Tests

To run all tests:

```bash
cd reanalyze
python -m pytest
```

To run tests with coverage:

```bash
cd reanalyze
python -m pytest --cov=. --cov-report=term --cov-report=html
```

This will generate a coverage report in the terminal and an HTML report in the `coverage_html_report` directory.

## Test Coverage

The tests aim to cover:

1. **Flask Application**
   - Endpoint functionality
   - Error handling
   - Environment variable validation

2. **External Service Interactions**
   - Delete service requests
   - Preprocess service requests
   - Error handling for external service failures

## Mocking

The tests use mocks to avoid making actual API calls to:
- Delete service
- Preprocess service

This ensures tests can run without external dependencies and are deterministic.
