"""
Pytest fixtures for the Reanalyze service tests.
"""

import os
import pytest
from unittest.mock import patch, MagicMock
import sys
from flask import Flask
from flask.testing import Flask<PERSON>lient

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture
def mock_env_variables():
    """Fixture to mock environment variables required by the app."""
    env_vars = {
        "DELETE_SERVICE_URL": "https://delete-service.example.com",
        "PREPROCESS_SERVICE_URL": "https://preprocess-service.example.com",
        "PROJECT_ID": "test-project-id",
        "ALLOWED_ORIGINS": "*"
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars

@pytest.fixture
def mock_requests():
    """Fixture to mock requests library."""
    with patch('requests.delete') as mock_delete:
        with patch('requests.post') as mock_post:
            # Create mock responses
            mock_delete_response = MagicMock()
            mock_delete_response.status_code = 200
            mock_delete.return_value = mock_delete_response
            
            mock_post_response = MagicMock()
            mock_post_response.status_code = 201
            mock_post.return_value = mock_post_response
            
            yield mock_delete, mock_post, mock_delete_response, mock_post_response

@pytest.fixture
def test_client(mock_env_variables, mock_requests):
    """Fixture to create a test client for the Flask app."""
    # Import the app here to ensure mocks are in place before app initialization
    from main import app
    
    # Configure the app for testing
    app.config['TESTING'] = True
    
    # Create a test client
    with app.test_client() as client:
        yield client
