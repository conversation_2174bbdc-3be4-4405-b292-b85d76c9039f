"""
Unit tests for the Reanalyze service.
"""

import pytest
import json
import os
from unittest.mock import patch, MagicMock
import requests
import firebase_admin
from firebase_admin import credentials
from firebase_admin import firestore

class TestReanalyze:
    def test_reanalyze_success(self, test_client, mock_requests):
        """Test reanalyze when successful."""
        mock_delete, mock_post, mock_delete_response, mock_post_response = mock_requests
        
        # Call the endpoint
        response = test_client.post('/reanalyze/test_rfp')
        
        # Assert that the response is correct
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["message"] == "Reanalysis of RFP test_rfp completed successfully"
        assert data["rfp_name"] == "test_rfp"
        
        # Assert that the delete request was made correctly
        mock_delete.assert_called_once_with("https://delete-service.example.com/delete/test_rfp")
        
        # Assert that the post request was made correctly
        mock_post.assert_called_once_with(
            "https://preprocess-service.example.com/preprocess",
            json={
                "excel_file_name": "uploads/test_rfp.xlsx",
                "rfp_bucket_name": "test-project-id.appspot.com"
            }
        )
    
    def test_reanalyze_missing_delete_service_url(self, test_client):
        """Test reanalyze when DELETE_SERVICE_URL is missing."""
        # Set up environment variables without DELETE_SERVICE_URL
        with patch.dict(os.environ, {
            "PREPROCESS_SERVICE_URL": "https://preprocess-service.example.com",
            "PROJECT_ID": "test-project-id"
        }, clear=True):
            # Call the endpoint
            response = test_client.post('/reanalyze/test_rfp')
            
            # Assert that the response indicates an error
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "DELETE_SERVICE_URL environment variable not set or empty" in data["error"]
    
    def test_reanalyze_missing_preprocess_service_url(self, test_client):
        """Test reanalyze when PREPROCESS_SERVICE_URL is missing."""
        # Set up environment variables without PREPROCESS_SERVICE_URL
        with patch.dict(os.environ, {
            "DELETE_SERVICE_URL": "https://delete-service.example.com",
            "PROJECT_ID": "test-project-id"
        }, clear=True):
            # Call the endpoint
            response = test_client.post('/reanalyze/test_rfp')
            
            # Assert that the response indicates an error
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "PREPROCESS_SERVICE_URL environment variable not set or empty" in data["error"]
    
    def test_reanalyze_missing_project_id(self, test_client, mock_requests):
        """Test reanalyze when PROJECT_ID is missing."""
        mock_delete, mock_post, mock_delete_response, mock_post_response = mock_requests
        
        # Set up environment variables without PROJECT_ID
        with patch.dict(os.environ, {
            "DELETE_SERVICE_URL": "https://delete-service.example.com",
            "PREPROCESS_SERVICE_URL": "https://preprocess-service.example.com"
        }, clear=True):
            # Call the endpoint
            response = test_client.post('/reanalyze/test_rfp')
            
            # Assert that the response indicates an error
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "PROJECT_ID environment variable not set or empty" in data["error"]
    
    def test_reanalyze_delete_error(self, test_client, mock_requests):
        """Test reanalyze when the delete request fails."""
        mock_delete, mock_post, mock_delete_response, mock_post_response = mock_requests
        
        # Make the delete request raise an exception
        mock_delete.side_effect = requests.exceptions.RequestException("Delete error")
        
        # Call the endpoint
        response = test_client.post('/reanalyze/test_rfp')
        
        # Assert that the response indicates an error
        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
        assert "Error deleting existing data: Delete error" in data["error"]
    
    def test_reanalyze_preprocess_error(self, test_client, mock_requests):
        """Test reanalyze when the preprocess request fails."""
        mock_delete, mock_post, mock_delete_response, mock_post_response = mock_requests
        
        # Make the post request raise an exception
        mock_post.side_effect = requests.exceptions.RequestException("Preprocess error")
        
        # Call the endpoint
        response = test_client.post('/reanalyze/test_rfp')
        
        # Assert that the response indicates an error
        assert response.status_code == 500
        data = json.loads(response.data)
        assert "error" in data
        assert "Error triggering preprocessing: Preprocess error" in data["error"]
    
    def test_reanalyze_delete_non_200(self, test_client, mock_requests):
        """Test reanalyze when the delete request returns a non-200 status code."""
        mock_delete, mock_post, mock_delete_response, mock_post_response = mock_requests
        
        # Make the delete response return a non-200 status code
        mock_delete_response.status_code = 404
        
        # Call the endpoint
        response = test_client.post('/reanalyze/test_rfp')
        
        # Assert that the response is still successful (the endpoint continues even if delete fails)
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["message"] == "Reanalysis of RFP test_rfp completed successfully"
        assert data["rfp_name"] == "test_rfp"
    
    def test_reanalyze_preprocess_non_201(self, test_client, mock_requests):
        """Test reanalyze when the preprocess request returns a non-201 status code."""
        mock_delete, mock_post, mock_delete_response, mock_post_response = mock_requests
        
        # Make the post response return a non-201 status code
        mock_post_response.status_code = 500
        
        # Call the endpoint
        response = test_client.post('/reanalyze/test_rfp')
        
        # Assert that the response is still successful (the endpoint continues even if preprocess fails)
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["message"] == "Reanalysis of RFP test_rfp completed successfully"
        assert data["rfp_name"] == "test_rfp"
