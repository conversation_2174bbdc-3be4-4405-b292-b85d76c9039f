"""
RFP Navigator Reanalyze Service

This module provides a Flask application that serves as the reanalyze service for the RFP Navigator.
It allows reprocessing RFP documents by first deleting existing data and then triggering the preprocessing pipeline.
"""

import os
import logging
from flask_cors import CORS
from flask import Flask, request, jsonify
import requests
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
allowed_origins = os.getenv("ALLOWED_ORIGINS", "*").split(",")
logger.info(f"Configuring CORS with allowed origins: {allowed_origins}")
CORS(app, resources={r"/*": {"origins": allowed_origins}})

@app.route('/reanalyze/<string:rfp_name>', methods=['POST'])
def reanalyze(rfp_name):
    """
    Reanalyze an RFP document by deleting existing data and triggering preprocessing.

    This endpoint:
    1. Deletes all existing data for the RFP
    2. Triggers the preprocessing pipeline to reanalyze the RFP

    Args:
        rfp_name (str): The name of the RFP to reanalyze.

    Returns:
        JSON response with reanalysis status.
    """
    logger.info(f"Reanalyzing RFP: {rfp_name}")

    # Get service URLs from environment variables
    delete_service_url = os.getenv("DELETE_SERVICE_URL")
    preprocess_service_url = os.getenv("PREPROCESS_SERVICE_URL")

    # Validate service URLs
    if not delete_service_url:
        logger.error("DELETE_SERVICE_URL environment variable not set or empty")
        return jsonify({"error": "DELETE_SERVICE_URL environment variable not set or empty"}), 500

    if not preprocess_service_url:
        logger.error("PREPROCESS_SERVICE_URL environment variable not set or empty")
        return jsonify({"error": "PREPROCESS_SERVICE_URL environment variable not set or empty"}), 500

    # Step 1: Delete existing data
    delete_url = f"{delete_service_url}/delete/{rfp_name}"
    try:
        logger.info(f"Deleting existing data for RFP: {rfp_name}")
        response = requests.delete(delete_url)
        if response.status_code == 200:
            logger.info(f"Existing data for RFP {rfp_name} deleted successfully")
        else:
            logger.warning(f"Delete service returned status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error deleting existing data: {str(e)}", exc_info=True)
        return jsonify({"error": f"Error deleting existing data: {str(e)}"}), 500

    # Step 2: Trigger preprocessing
    preprocess_url = f"{preprocess_service_url}/preprocess"

    # Get project ID from environment variables
    project_id = os.getenv("PROJECT_ID")

    # Validate project ID
    if not project_id:
        logger.error("PROJECT_ID environment variable not set or empty")
        return jsonify({"error": "PROJECT_ID environment variable not set or empty"}), 500

    # Prepare payload for preprocessing
    preprocess_payload = {
        "excel_file_name": f"uploads/{rfp_name}.xlsx",
        "rfp_bucket_name": f"{project_id}.appspot.com"
    }

    try:
        logger.info(f"Triggering preprocessing for RFP: {rfp_name}")
        response = requests.post(preprocess_url, json=preprocess_payload)
        if response.status_code == 201:
            logger.info(f"Preprocessing triggered successfully for RFP {rfp_name}")
        else:
            logger.warning(f"Preprocess service returned status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error triggering preprocessing: {str(e)}", exc_info=True)
        return jsonify({"error": f"Error triggering preprocessing: {str(e)}"}), 500

    logger.info(f"Reanalysis of RFP {rfp_name} completed successfully")
    return jsonify({
        "message": f"Reanalysis of RFP {rfp_name} completed successfully",
        "rfp_name": rfp_name
    })


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001)